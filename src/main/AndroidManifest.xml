<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.jiguang.jpush">
    <application>
<!--        <receiver-->
<!--            android:name="com.jiguang.jpush.JPushPlugin$JPushReceiver"-->
<!--            android:enabled="true"-->
<!--            android:exported="false">-->
<!--            <intent-filter>-->
<!--                <action android:name="cn.jpush.android.intent.REGISTRATION" />-->
<!--                <action android:name="cn.jpush.android.intent.MESSAGE_RECEIVED" />-->
<!--                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED" />-->
<!--                <action android:name="cn.jpush.android.intent.NOTIFICATION_OPENED" />-->
<!--                <action android:name="cn.jpush.android.intent.NOTIFICATION_CLICK_ACTION" />-->
<!--                <action android:name="cn.jpush.android.intent.CONNECTION" />-->
<!--                <category android:name="${applicationId}" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

        <receiver android:name="com.jiguang.jpush.JPushEventReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.RECEIVER_MESSAGE" />
                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver>
        <!-- Since JCore2.0.0 Required SDK核心功能-->
        <!-- 可配置android:process参数将Service放在其他进程中；android:enabled属性不能是false -->
        <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
        <service android:name=".JPushCustomService"
            android:enabled="true"
            android:exported="false"
            android:stopWithTask="true"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jiguang.user.service.action" />
            </intent-filter>
        </service>
    </application>
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE"/>
</manifest>
