// Autogenerated from <PERSON><PERSON> (v25.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon
@file:Suppress("UNCHECKED_CAST", "ArrayInDataClass")


import android.util.Log
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.StandardMethodCodec
import io.flutter.plugin.common.StandardMessageCodec
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

private fun wrapResult(result: Any?): List<Any?> {
  return listOf(result)
}

private fun wrapError(exception: Throwable): List<Any?> {
  return if (exception is WakelockPlusFlutterError) {
    listOf(
      exception.code,
      exception.message,
      exception.details
    )
  } else {
    listOf(
      exception.javaClass.simpleName,
      exception.toString(),
      "Cause: " + exception.cause + ", Stacktrace: " + Log.getStackTraceString(exception)
    )
  }
}

/**
 * Error class for passing custom error details to <PERSON>lutter via a thrown PlatformException.
 * @property code The error code.
 * @property message The error message.
 * @property details The error details. Must be a datatype supported by the api codec.
 */
class WakelockPlusFlutterError (
  val code: String,
  override val message: String? = null,
  val details: Any? = null
) : Throwable()
private fun deepEqualsWakelockPlusMessages(a: Any?, b: Any?): Boolean {
  if (a is ByteArray && b is ByteArray) {
      return a.contentEquals(b)
  }
  if (a is IntArray && b is IntArray) {
      return a.contentEquals(b)
  }
  if (a is LongArray && b is LongArray) {
      return a.contentEquals(b)
  }
  if (a is DoubleArray && b is DoubleArray) {
      return a.contentEquals(b)
  }
  if (a is Array<*> && b is Array<*>) {
    return a.size == b.size &&
        a.indices.all{ deepEqualsWakelockPlusMessages(a[it], b[it]) }
  }
  if (a is List<*> && b is List<*>) {
    return a.size == b.size &&
        a.indices.all{ deepEqualsWakelockPlusMessages(a[it], b[it]) }
  }
  if (a is Map<*, *> && b is Map<*, *>) {
    return a.size == b.size && a.all {
        (b as Map<Any?, Any?>).containsKey(it.key) &&
        deepEqualsWakelockPlusMessages(it.value, b[it.key])
    }
  }
  return a == b
}
    

/**
 * Message for toggling the wakelock on the platform side.
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class ToggleMessage (
  val enable: Boolean? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): ToggleMessage {
      val enable = pigeonVar_list[0] as Boolean?
      return ToggleMessage(enable)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      enable,
    )
  }
  override fun equals(other: Any?): Boolean {
    if (other !is ToggleMessage) {
      return false
    }
    if (this === other) {
      return true
    }
    return deepEqualsWakelockPlusMessages(toList(), other.toList())  }

  override fun hashCode(): Int = toList().hashCode()
}

/**
 * Message for reporting the wakelock state from the platform side.
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class IsEnabledMessage (
  val enabled: Boolean? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): IsEnabledMessage {
      val enabled = pigeonVar_list[0] as Boolean?
      return IsEnabledMessage(enabled)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      enabled,
    )
  }
  override fun equals(other: Any?): Boolean {
    if (other !is IsEnabledMessage) {
      return false
    }
    if (this === other) {
      return true
    }
    return deepEqualsWakelockPlusMessages(toList(), other.toList())  }

  override fun hashCode(): Int = toList().hashCode()
}
private open class WakelockPlusMessagesPigeonCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      129.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          ToggleMessage.fromList(it)
        }
      }
      130.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          IsEnabledMessage.fromList(it)
        }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }
  override fun writeValue(stream: ByteArrayOutputStream, value: Any?)   {
    when (value) {
      is ToggleMessage -> {
        stream.write(129)
        writeValue(stream, value.toList())
      }
      is IsEnabledMessage -> {
        stream.write(130)
        writeValue(stream, value.toList())
      }
      else -> super.writeValue(stream, value)
    }
  }
}

/** Generated interface from Pigeon that represents a handler of messages from Flutter. */
interface WakelockPlusApi {
  fun toggle(msg: ToggleMessage)
  fun isEnabled(): IsEnabledMessage

  companion object {
    /** The codec used by WakelockPlusApi. */
    val codec: MessageCodec<Any?> by lazy {
      WakelockPlusMessagesPigeonCodec()
    }
    /** Sets up an instance of `WakelockPlusApi` to handle messages through the `binaryMessenger`. */
    @JvmOverloads
    fun setUp(binaryMessenger: BinaryMessenger, api: WakelockPlusApi?, messageChannelSuffix: String = "") {
      val separatedMessageChannelSuffix = if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.wakelock_plus_platform_interface.WakelockPlusApi.toggle$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val msgArg = args[0] as ToggleMessage
            val wrapped: List<Any?> = try {
              api.toggle(msgArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.wakelock_plus_platform_interface.WakelockPlusApi.isEnabled$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.isEnabled())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
