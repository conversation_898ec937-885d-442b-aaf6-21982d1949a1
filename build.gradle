File pubspec = new File(project.projectDir.parentFile, 'pubspec.yaml')
String yaml = pubspec.text
java.util.regex.Matcher versionMatcher = java.util.regex.Pattern.compile("^version:\\s*['|\"]?([^\\n|'|\"]*)['|\"]?\$", java.util.regex.Pattern.MULTILINE).matcher(yaml)
versionMatcher.find()
String library_version = versionMatcher.group(1).replaceAll("\\+", "-")

group 'com.fluttercandies.photo_manager'
version library_version

buildscript {
    ext.kotlin_version = '1.9.23'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    if (project.android.hasProperty('namespace')) {
        namespace 'com.flutterandies.photo_manager'
    }

    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 16
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
}

dependencies {
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    implementation 'androidx.exifinterface:exifinterface:1.3.7'
}
