group 'uz.shs.better_player_plus'
version '1.0-SNAPSHOT'

buildscript {
    ext.lifecycleVersion = "2.7.0"
    ext.annotationVersion = "1.7.0"
    ext.media3_version = "1.3.1"
    ext.workVersion = "2.9.0"
    ext.kotlinVersion = "1.9.22"

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdk 34
    namespace "uz.shs.better_player_plus"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        minSdkVersion 21
    }

    dependencies {
        implementation "androidx.media:media:1.7.0"
        implementation "androidx.media3:media3-ui:$media3_version"
        implementation "androidx.media3:media3-session:$media3_version"
        implementation "androidx.media3:media3-exoplayer:$media3_version"
        implementation "androidx.media3:media3-exoplayer-hls:$media3_version"
        implementation "androidx.media3:media3-exoplayer-dash:$media3_version"
        implementation "androidx.media3:media3-datasource-cronet:$media3_version"
        implementation "androidx.media3:media3-exoplayer-smoothstreaming:$media3_version"

        implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycleVersion"
        implementation "androidx.lifecycle:lifecycle-common:$lifecycleVersion"
        implementation "androidx.lifecycle:lifecycle-common-java8:$lifecycleVersion"
        implementation "androidx.annotation:annotation:$annotationVersion"
        implementation "androidx.work:work-runtime:$workVersion"
    }
}

dependencies {
    //noinspection GradleDependency
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlinVersion"
}
