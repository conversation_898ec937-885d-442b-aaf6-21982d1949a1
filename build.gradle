group 'com.ryanheise.just_audio'
version '1.0'
def args = ["-Xlint:deprecation","-Xlint:unchecked"]

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

project.getTasks().withType(JavaCompile) {
    options.compilerArgs.addAll(args)
}

apply plugin: 'com.android.library'

android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
        namespace 'com.ryanheise.just_audio'
    }
    compileSdk 34

    defaultConfig {
        minSdk 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lintOptions {
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency', 'NewerVersionAvailable'
    }

    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }

    dependencies {
        def exoplayer_version = "2.18.7"
        implementation "com.google.android.exoplayer:exoplayer-core:$exoplayer_version"
        implementation "com.google.android.exoplayer:exoplayer-dash:$exoplayer_version"
        implementation "com.google.android.exoplayer:exoplayer-hls:$exoplayer_version"
        implementation "com.google.android.exoplayer:exoplayer-smoothstreaming:$exoplayer_version"
    }
}

