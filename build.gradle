def args = ["-Xlint:deprecation"]

group 'com.tundralabs.fluttertts'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.9.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

project.getTasks().withType(JavaCompile).configureEach {
    options.compilerArgs.addAll(args)
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdk 34
    if (project.android.hasProperty("namespace")) {
        namespace 'com.tundralabs.fluttertts'
    }

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lintOptions {
        disable 'InvalidPackage'
        disable 'GradleDependency'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }
}
dependencies {
    implementation "androidx.core:core-ktx:1.8.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
}
repositories {
    mavenCentral()
}
