group 'com.jiguang.jpush'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        mavenCentral()
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'
    }
}

rootProject.allprojects {
    repositories {
        mavenCentral()
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    if (project.android.hasProperty("namespace")) {
        namespace = "com.jiguang.jpush"
    }

    compileSdk 34
    defaultConfig {
        minSdk 19
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    implementation 'cn.jiguang.sdk:jpush:5.7.0'

}