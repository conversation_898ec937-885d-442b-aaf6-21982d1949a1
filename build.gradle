group = "com.ryanheise.audio_session"
version "1.0"
def args = ["-Xlint:deprecation","-Xlint:unchecked"]

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:8.1.0")
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

project.getTasks().withType(JavaCompile) {
    options.compilerArgs.addAll(args)
}

apply plugin: "com.android.library"

android {
    if (project.android.hasProperty("namespace")) {
        namespace = "com.ryanheise.audio_session"
    }

    compileSdk = 34

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdk = 19
    }

    lintOptions {
        checkAllWarnings true
        warningsAsErrors true
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency', 'NewerVersionAvailable'
    }
}

dependencies {
    implementation "androidx.media:media:1.7.0"
}
