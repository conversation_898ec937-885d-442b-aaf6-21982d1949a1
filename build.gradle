group 'com.signify.hue.flutterreactiveblelib'
version '1.0-SNAPSHOT'

buildscript {
    ext.detekt_version = '1.23.0'
    ext.kotlin_version = '1.8.21'
    repositories {
        google()
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.2'
        classpath 'com.google.protobuf:protobuf-gradle-plugin:0.9.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "io.gitlab.arturbosch.detekt:detekt-gradle-plugin:$detekt_version"
        classpath "de.mannodermaus.gradle.plugins:android-junit5:*******"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'com.google.protobuf'
apply plugin: 'kotlin-android'
apply plugin: "io.gitlab.arturbosch.detekt"
apply plugin: "de.mannodermaus.android-junit5"

android {
    compileSdkVersion 33
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        test.java.srcDirs += 'src/test/kotlin'
        main {
            proto {
                srcDir '../protos/'
            }
        }
    }

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33
        consumerProguardFiles 'proguard-rules.txt'
    }

    namespace 'com.signify.hue.flutterreactiveble'

    lint {
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8.toString()
    }
}

detekt {
    version = detekt_version
    input = files("src/main/kotlin")
    dependencies {
        detektPlugins "io.gitlab.arturbosch.detekt:detekt-formatting:$detekt_version"
        detektPlugins "io.gitlab.arturbosch.detekt:detekt-rules-libraries:$detekt_version"
    }
}

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.25.3'
    }

    generateProtoTasks {
        all().each { task ->
            task.builtins {
                java {
                    option "lite"
                }
            }
        }
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.polidea.rxandroidble2:rxandroidble:1.16.0'
    implementation 'com.google.protobuf:protobuf-javalite:3.25.3'
    implementation 'io.reactivex.rxjava2:rxkotlin:2.4.0'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'

    testImplementation "org.junit.jupiter:junit-jupiter-api:5.7.0"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:5.7.0"
    testImplementation "io.mockk:mockk:1.11.0"
    testImplementation "com.google.truth:truth:1.1.4"
}