group 'io.flutter.plugins.imagepicker'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.1'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'io.flutter.plugins.imagepicker'
    compileSdk 34

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        checkAllWarnings true
        warningsAsErrors true
        disable 'AndroidGradlePluginVersion', 'InvalidPackage', 'GradleDependency', 'NewerVersionAvailable'
    }
    dependencies {
        implementation 'androidx.core:core:1.13.1'
        implementation 'androidx.annotation:annotation:1.9.1'
        implementation 'androidx.exifinterface:exifinterface:1.3.7'
        implementation 'androidx.activity:activity:1.9.3'

        testImplementation 'junit:junit:4.13.2'
        testImplementation 'org.mockito:mockito-core:5.1.1'
        testImplementation 'androidx.test:core:1.4.0'
        testImplementation "org.robolectric:robolectric:4.10.3"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
