buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()
        maven { setUrl("https://maven.aliyun.com/repository/public") }
        maven { setUrl("https://maven.aliyun.com/repository/google") }

        // hms
        maven { url 'https://developer.huawei.com/repo/'}

        // 荣耀
        maven {
            url 'https://developer.hihonor.com/repo/'
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

        // hms
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'

        // 增加asplugin插件配置，推荐您使用最新版本。
        classpath 'com.hihonor.mcs:asplugin:2.0.1.300'

    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { setUrl("https://maven.aliyun.com/repository/public") }
        maven { setUrl("https://maven.aliyun.com/repository/google") }

        // hms
        maven { url 'https://developer.huawei.com/repo/'}

        // 荣耀
        maven {
            url 'https://developer.hihonor.com/repo/'
        }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
