package com.hengyue.transmitter

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.hengyue.DynamicLoader
import com.hengyue.utils.ByteUtils
import com.microtech.xlib.CGMManager
import com.microtech.xlib.ble.device.OnDataChangeListener
import com.microtech.xlib.ble.operation.OPERATION_SUCCESS
import com.microtech.xlib.ble.operation.OperationCallback
import com.microtech.xlib.util.LogListener
import com.microtech.xlib.util.LogUtil
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.Date

/** TransmitterPlugin */
class TransmitterPlugin : FlutterPlugin, MethodCallHandler, EventChannel.StreamHandler {
    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    private lateinit var channel: MethodChannel
    private lateinit var microtechMethodChannel: MethodChannel
    private lateinit var microtechEventChannel: EventChannel
    private lateinit var microtechDateChangeEventChannel: EventChannel
    private lateinit var application: Context
    private lateinit var gson: Gson
    private lateinit var algorithmMethodChannel: MethodChannel
    private lateinit var algorithmSplitMethodChannel: MethodChannel

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        application = flutterPluginBinding.applicationContext
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "transmitter")
        channel.setMethodCallHandler(this)
        microtechMethodChannel = MethodChannel(
            flutterPluginBinding.binaryMessenger,
            "hengyue.transmitter/microtech_method"
        )
        microtechEventChannel = EventChannel(
            flutterPluginBinding.binaryMessenger,
            "hengyue.transmitter/microtech_event"
        )
        microtechDateChangeEventChannel = EventChannel(
            flutterPluginBinding.binaryMessenger,
            "hengyue.transmitter/microtech_data_change_event"
        )
        algorithmMethodChannel = MethodChannel(
            flutterPluginBinding.binaryMessenger,
            "hengyue.transmitter/algorithm_method"
        )
        algorithmSplitMethodChannel = MethodChannel(
            flutterPluginBinding.binaryMessenger,
            "hengyue.transmitter/algorithm_split_method"
        )
        algorithmSplitMethodChannel.setMethodCallHandler(SisensingSplitMethodCallHandler(application))
        algorithmMethodChannel.setMethodCallHandler(AlgorithmMethodCallHandler(application))
        microtechMethodChannel.setMethodCallHandler(this)
        microtechEventChannel.setStreamHandler(this)
        microtechDateChangeEventChannel.setStreamHandler(object : EventChannel.StreamHandler {
            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                Log.e("transmitter", "onListen data change")
                CGMManager.instance().setOnDataChangeListener(object : OnDataChangeListener {
                    override fun onDataChange() {
                        Log.e("transmitter", "onListen data change call back")
                        events?.success(true)
                    }
                });
            }

            override fun onCancel(p0: Any?) {
                CGMManager.instance().setOnDataChangeListener(object : OnDataChangeListener {
                    override fun onDataChange() {

                    }
                });
            }

        });
        LogUtil.setLogListener(object : LogListener {
            override fun onLog(log: String) {
//        Log.e("AiDEX-SDK-LOG-HY", log)
                GlobalScope.launch(Dispatchers.Main) {
                    microtechMethodChannel.invokeMethod("aidexSdkLog", "AiDEX-SDK-LOG $log")
                }
            }
        })
        gson = Gson()
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        try {
            Log.e("transmitter", "method = ${call.method} argument ${call.arguments}")
            if (call.method == "getPlatformVersion") {
                result.success("Android ${android.os.Build.VERSION.RELEASE}")
            } else if (call.method == "history_time_range") {
                val sn = call.argument<String>("sn") ?: ""
                try {
                    CGMManager.instance().getHistoryTimeRange(sn)
                }catch (e:Exception){
                    Log.e("transmitter","history_time_range ${e.message}")
                }
            } else if (call.method == "history_data_by_time_range") {
                val sn = call.argument<String>("sn") ?: ""
                val start = call.argument<String>("start") ?: ""
                val end = call.argument<String>("end") ?: ""
                val startDate = Date(start.toLong())
                val endDate = Date(end.toLong())
                GlobalScope.launch {
                    try {
                        var resultBean =
                            CGMManager.instance().getCGMHistoryByTimeRange(sn, startDate, endDate)
                        result.success(gson.toJson(resultBean))
                    }catch (e:Exception){
                        Log.e("transmitter","history_data_by_time_range ${e.message}")
                    }
                }
            } else if (call.method == "delete") {
                CGMManager.instance().delete(object : OperationCallback {
                    override fun onFailure(code: Int) {
                        try {
                            result.success(code)
                        } catch (e: Exception) {
                            Log.e("transmitter","delete failure ${e.message}")
                        }
                    }

                    override fun onSuccess() {
                        try {
                            result.success(OPERATION_SUCCESS)
                        } catch (e: Exception) {
                            Log.e("transmitter","delete success ${e.message}")
                        }
                    }

                })
            } else if (call.method == "unpair") {
                CGMManager.instance().unpair(object : OperationCallback {
                    override fun onFailure(code: Int) {
                        try {
                            result.success(code)
                        } catch (e: Exception) {
                            Log.e("transmitter","unpair failure ${e.message}")
                        }
                    }

                    override fun onSuccess() {
                        try {
                            result.success(OPERATION_SUCCESS)
                        } catch (e: Exception) {
                            Log.e("transmitter","unpair success ${e.message}")
                        }
                    }

                })

            } else if (call.method == "new_sensor") {
                CGMManager.instance().newSensor(object : OperationCallback {
                    override fun onFailure(code: Int) {
                        try {
                            result.success(code)
                        } catch (e: Exception) {
                            Log.e("transmitter","new_sensor failure ${e.message}")
                        }
                    }

                    override fun onSuccess() {
                        try {
                            result.success(OPERATION_SUCCESS)
                        } catch (e: Exception) {
                            Log.e("transmitter","new_sensor success ${e.message}")
                        }
                    }

                })
            } else if (call.method == "pair_information") {
                Log.e("transmitter", "pair_information")
                GlobalScope.launch {
                    try {
                        Log.e("transmitter", "CGMManager.instance().getPairInformation")
                        val pairInfo = CGMManager.instance().getPairInformation()
                        Log.e("transmitter", "CGMManager.instance().getPairInformation result")
                        result.success(gson.toJson(SensorBean(pairInfo)))
                    } catch (e: Exception) {
                        Log.e("transmitter","pair_information success ${e.message}")
                    }
                }
            } else if (call.method == "pair") {
                val sn = call.argument<String>("sn") ?: ""
                CGMManager.instance().pair(sn, object : OperationCallback {
                    override fun onFailure(code: Int) {
                        try {
                            result.success(code)
                        } catch (e: Exception) {
                            Log.e("transmitter","pair failure ${e.message}")
                        }
                    }

                    override fun onSuccess() {
                        try {
                            result.success(OPERATION_SUCCESS)
                        } catch (e: Exception) {
                            Log.e("transmitter","pair success ${e.message}")
                        }
                    }
                })
            } else if (call.method == "sensor_status") {
                try {
                    CGMManager.instance().getSensorStatus()?.let {
                        result.success(gson.toJson(SensorStatusBean(it)))
                    }
                } catch (e: Exception) {
                    Log.e("transmitter","sensor_status success ${e.message}")
                }
            } else if (call.method == "init") {
                val secret = call.argument<String>("secret") ?: ""
                Log.e("transmitter", "CGMManager.instance().init")
                CGMManager.instance().init(application, secret, object : OperationCallback {
                    override fun onFailure(code: Int) {
                        Log.e("transmitter", "init onFailure" + code)
                        try {
                            result.success(code)
                        } catch (e: Exception) {
                            Log.e("transmitter","init failure ${e.message}")
                        }
                    }

                    override fun onSuccess() {
                        Log.e("transmitter", "init onSuccess")
                        try {
                            result.success(OPERATION_SUCCESS)
                        } catch (e: Exception) {
                            Log.e("transmitter","init success ${e.message}")
                        }
                    }
                })
            } else if (call.method == "calibrate") {
                val value = call.argument<Int>("value") ?: 0
                Log.e("transmitter", "CGMManager.instance().calibrate")
                CGMManager.instance().calibrate(value, object : OperationCallback {
                    override fun onFailure(code: Int) {
                        Log.e("transmitter","calibrate $code")
                        try {
                            result.success(code)
                        }catch (e:Exception){
                            Log.e("transmitter","calibrate failure ${e.message}")
                        }
                    }

                    override fun onSuccess() {
                        Log.e("transmitter","calibrate success")
                        try{
                            result.success(OPERATION_SUCCESS)
                        }catch (e:Exception){
                            Log.e("transmitter","calibrate success ${e.message}")
                        }
                    }

                })
            } else {
                result.notImplemented()
            }
        } catch (e: Exception) {
            Log.e("onMethodCall","触发异常")
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        microtechMethodChannel.setMethodCallHandler(null)
        microtechEventChannel.setStreamHandler(null)
        microtechDateChangeEventChannel.setStreamHandler(null)
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        CGMManager.instance().setSensorStatusChangeListener {
            events?.success(gson.toJson(SensorStatusBean(it)))
        }
    }

    override fun onCancel(arguments: Any?) {
        CGMManager.instance().setSensorStatusChangeListener {}
    }
}
const val kAlgorithmContext:String = "algorithm_context";
const val kAlgorithmContextName:String = "algorithm_context_name";

class AlgorithmMethodCallHandler(val context: Context) : MethodCallHandler {
    private var softwareVersion:String = ""
    private var sp :SharedPreferences = context.getSharedPreferences(kAlgorithmContextName, Context.MODE_PRIVATE)
    private var linkCode:String = ""

    override fun onMethodCall(call: MethodCall, result: Result) {
        try {
            Log.e(
                "transmitter",
                "AlgorithmMethodCallHandler method = ${call.method} argument ${call.arguments}"
            )
            when (call.method) {
                "init" -> {
                    linkCode = call.argument<String>("linkCode") ?: ""
                    var algorithmContext = call.argument<String>("algorithmContext") ?: ""
                    val resultCode = DynamicLoader.getIAlgorithm(context).verifyLinkCode(linkCode)
                    if (resultCode != 1) {
                        result.success(false)
                        return;
                    }
                    DynamicLoader.getIAlgorithm(context).initAlgorithmContext(algorithmContext)
                    result.success(true)
                }

                "load_data" -> {
                    val hexString = call.argument<String>("hexString") ?: ""
                    // 将十六进制字符串转换为十进制整数数组
                    val testV1: ByteArray = ByteUtils.hexStringToIntArray(hexString)
                    val json = DynamicLoader.getIAlgorithmData(context).splitData(testV1,DynamicLoader.getIAlgorithm(context),softwareVersion)
//                    val algorithmContext = DynamicLoader.getIAlgorithm(context).algorithmContext
                    // 保证不错乱采用同步提交
//                    sp.edit().putString("${kAlgorithmContext}_${linkCode}",algorithmContext).commit();
//                    result.success(json)
//                    val algorithmContext = createAlgorithm.algorithmContext
                    result.success("{\"data\":$json}");
                }

                "software_version" -> {
                    val version = call.argument<String>("software_version") ?: ""
                    softwareVersion = DynamicLoader.getIAlgorithmData(context).getSoftwareVersion(version).toString()
                    result.success(softwareVersion)
                }

                "encrypt_instruction" -> {
                    val version = call.argument<String>("software_version") ?: ""
                    val device = call.argument<String>("device_id") ?: ""
                    val index = call.argument<Int>("index") ?: 1
                    val timeMillis = call.argument<String>("time_millis")
                    val data = DynamicLoader.getIAlgorithmData(context).getActivation(version,device,index)
                    timeMillis?.let {
                        DynamicLoader.getIAlgorithmData(context).setNewTimeMills(it.toLong())
                        DynamicLoader.getIAlgorithmData(context).setIndex(index)
                    }

                    result.success(ByteUtils.byteArrayToHexString(data))
                }

                else -> {
                    result.notImplemented()
                }
            }
        } catch (e: Exception) {

        }
    }

}
