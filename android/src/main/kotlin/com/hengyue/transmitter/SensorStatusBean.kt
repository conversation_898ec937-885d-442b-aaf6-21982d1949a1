package com.hengyue.transmitter

import com.microtech.xlib.entity.SensorStatusEntity

class SensorStatusBean(entity: SensorStatusEntity) {
    var glucose: Float? = entity.glucose

    var glucoseTime: Long? = entity.glucoseTime?.time

    var glucoseTrend: com.microtech.xlib.ble.device.DeviceModel.GlucoseTrend? = entity.glucoseTrend

    var isPaired: Boolean? = entity.isPaired

    var remainHour: Int? = entity.remainHour

    var state: com.microtech.xlib.state.SensorStatus = entity.state

    var timeOffset: Int? = entity.timeOffset

    var warmUpRemainTime: Int? = entity.warmUpRemainTime
}