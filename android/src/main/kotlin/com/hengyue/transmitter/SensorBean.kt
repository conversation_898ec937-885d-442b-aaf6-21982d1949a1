package com.hengyue.transmitter

import com.microtech.xlib.entity.SensorEntity

class SensorBean (entity: SensorEntity?){

    var briefIndex:Int = entity?.briefIndex ?:-1
    var calIndex:Int = entity?.calIndex ?:-1
    var deviceKey:ByteArray? = entity?.deviceKey
    var deviceMac:String? = entity?.deviceMac
    var deviceModel:Int = entity?.deviceModel ?: -1
    var deviceName:String? = entity?.deviceName
    var deviceSn:String? = entity?.deviceSn
    var idx:Long? = entity?.idx
    var isUnpaired:Boolean = entity?.isUnpaired ?: false
    var needReplaceInfo:Pair<Boolean,Int>? = entity?.needReplaceInfo
    var other: String? = entity?.other
    var rawIndex: Int = entity?.rawIndex ?: -1
    var sensorId: String? = entity?.sensorId
    var sensorLifeTime: Int = entity?.sensorLifeTime ?: -1
    var sensorStartTime: Long? = entity?.sensorStartTime?.time
    var smoothVersion: String? = entity?.smoothVersion
    var uploadState: Int = entity?.uploadState ?: -1
    var userId: String? = entity?.userId
    var version: String? = entity?.version
}