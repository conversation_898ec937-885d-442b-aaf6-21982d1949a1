package com.hengyue.transmitter

import android.content.Context
import android.util.Log
import com.hengyue.DynamicLoader
import com.hengyue.utils.ByteUtils
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.io.ByteArrayOutputStream

class SisensingSplitMethodCallHandler(val context: Context) : MethodCallHandler  {

    private var softwareVersion:String = ""
    private var originalSoftVersion:String = ""
    private var linkCode:String = ""
//    private var iSplitAlgorithmData:ISplitAlgorithmData
//    private var iSplitAlgorithm:ISplitAlgorithm

//    init {
//        iSplitAlgorithmData = DynamicLoader.getISplitAlgorithmData(context)
//        iSplitAlgorithm = DynamicLoader.getISplitAlgorithm(context)
//    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        try {
            Log.e(
                "transmitter",
                "SisensingSplitMethodCallHandler method = ${call.method} argument ${call.arguments}"
            )
            when (call.method) {
                "registerKey" -> {
                    val resultCode = DynamicLoader.getISplitAlgorithmData(context).v120RegisterKey()
                    result.success(resultCode)
                }
                "auth" -> {
                    val mac = call.argument<String>("mac") ?: ""
                    val resultBuffer = DynamicLoader.getISplitAlgorithmData(context).V120ApplyAuthentication(mac)
                    result.success(resultBuffer)
                }
                "init" -> {
                    linkCode = call.argument<String>("linkCode") ?: ""
                    var algorithmContext = call.argument<String>("algorithmContext") ?: ""
                    val resultCode = DynamicLoader.getISplitAlgorithm(context).verifyLinkCode(linkCode)
                    if (resultCode != 1) {
                        result.success(false)
                        return;
                    }
                    DynamicLoader.getISplitAlgorithm(context).initAlgorithmContext(algorithmContext)
                    result.success(true)
                }

                "load_data" -> {
                    val hexString = call.argument<String>("hexString") ?: ""
                    // 将十六进制字符串转换为十进制整数数组
                    val testV1: ByteArray = ByteUtils.hexStringToIntArray(hexString)
                    val json = DynamicLoader.getISplitAlgorithmData(context).splitData(
                        testV1,
                        DynamicLoader.getISplitAlgorithm(context),
                        softwareVersion,
                        originalSoftVersion,
                    )
                    if(json.isNullOrBlank()){
                        result.success("{}")
                    }else{
                        result.success(json)
                    }
                }

                "software_version" -> {
                    originalSoftVersion = call.argument<String>("software_version") ?: ""
                    softwareVersion = DynamicLoader.getISplitAlgorithmData(context).getSoftwareVersion(originalSoftVersion).toString()
                    result.success(softwareVersion)
                }

                "encrypt_instruction" -> {
                    val version = call.argument<String>("software_version") ?: ""
                    val device = call.argument<String>("device_id") ?: ""
                    val index = call.argument<Int>("index") ?: 1
                    val timeMillis = call.argument<String>("time_millis")
                    val data = DynamicLoader.getISplitAlgorithmData(context).getActivation(version,device,index)
                    timeMillis?.let {
                        DynamicLoader.getISplitAlgorithmData(context).setNewTimeMills(it.toLong())
                        DynamicLoader.getISplitAlgorithmData(context).setIndex(index)
                    }
                    result.success(ByteUtils.byteArrayToHexString(data))
                }

                else -> {
                    result.notImplemented()
                }
            }
        } catch (_: Exception) {

        }
    }

    private fun hexStringToByteArray(digits: String): ByteArray {
        var b: Int
        var i: Int
        val baos = ByteArrayOutputStream()
        var i2 = 0
        while (i2 < digits.length) {
            try {
                val c1 = digits[i2]
                require(i2 + 1 < digits.length) { "hexUtil.odd" }
                val c2 = digits[i2 + 1].code
                b = if (c1 in '0'..'9') {
                    (0 + ((c1.code - '0'.code) * 16)).toByte().toInt()
                } else if (c1 in 'a'..'f') {
                    (0 + (((c1.code - 'a'.code) + 10) * 16)).toByte()
                        .toInt()
                } else if (c1 in 'A'..'F') {
                    (0 + (((c1.code - 'A'.code) + 10) * 16)).toByte()
                        .toInt()
                } else {
                    throw java.lang.IllegalArgumentException("hexUtil.bad")
                }
                i = if (c2 in 48..57) {
                    c2 - 48
                } else if (c2 in 97..102) {
                    c2 - 97 + 10
                } else if (c2 in 65..70) {
                    c2 - 65 + 10
                } else {
                    throw java.lang.IllegalArgumentException("hexUtil.bad")
                }
                baos.write((b + i).toByte().toInt())
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
            i2 += 2
        }
        return baos.toByteArray()
    }

    private fun logByteArray(byteArray: ByteArray) {
        // 将 ByteArray 转换为十六进制字符串
        val hexString = byteArray.joinToString(separator = "") { byte ->
            String.format("%02X", byte)  // 格式化为两位十六进制
        }

        // 打印日志
        Log.d("ByteArrayLog", "Byte array in hex: $hexString")
    }
}