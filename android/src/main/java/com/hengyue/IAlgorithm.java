package com.hengyue;

public interface IAlgorithm {
    String getAlgorithmContext();

    String getAlgorithmVersion();

    int getCgmWarning();

    int getCurrentWarning();

    int getGlucoseTrend();

    int getTempWarning();

    void initAlgorithmContext(String algorithmContext);

    float loadData(int index, float currentData, float tempData, float lowAlarmValue, float bgData, float highAlarmValue);

    void releaseAlgorithmContext(String userId, String deviceName);

    void saveAlgorithmContext(String userId, String deviceName, int index);

    int verifyLinkCode(String linkCode);
}