package com.hengyue;

public interface ISplitAlgorithmData {

    void setNewTimeMills(long newTimeMills);

    void setIndex(int index);

    String splitData(byte[] bArr, ISplitAlgorithm iAlgorithm, String softVersion, String originalSoftVersion);

    String getSoftwareVersion(String version);

    byte[] getActivation(String version, String deviceId, int index);

    int v120RegisterKey();

    byte[] V120ApplyAuthentication(String mac);
}
