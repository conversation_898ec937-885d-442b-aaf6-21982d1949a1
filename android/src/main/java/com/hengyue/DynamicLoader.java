package com.hengyue;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.lang.reflect.Method;

import dalvik.system.DexClassLoader;

public class DynamicLoader {

    private final static String TAG = "DanymicLoader";

    private static DexClassLoader dexClassLoader;

    private static DexClassLoader ecoDexClassLoader;

    private static IAlgorithm iAlgorithm;

    private static IAlgorithmData iAlgorithmData;

    private static ISplitAlgorithm iSplitAlgorithm;

    private static ISplitAlgorithmData iSplitAlgorithmData;

    // 0 默认值 载入硅基动弹 1 载入硅基轻享 2
    public static int loadSoType = 0;

    /**
     * 加载dex文件中的class，并调用其中的方法
     * 这里由于是加载 jar文件，所以采用DexClassLoader
     * 下面开始加载dex class
     */
    public static IAlgorithm getIAlgorithm(Context context) throws NoSuchMethodException {
        if(iAlgorithm == null) {
            try {
                Class<?> libClazz = loadDex(context).loadClass("com.hengyue.sisensing.AlgorithmFactory");
                Method method = libClazz.getMethod("createAlgorithm", String.class);
                iAlgorithm = (IAlgorithm) method.invoke(null, "ALGORITHM 1.1.2F(2024_03_08)");
            } catch (Exception e) {
                e.printStackTrace();
                Log.d(TAG, "getMethod error: " + e.getMessage());
            }
        }
        return iAlgorithm;
    }

    public static IAlgorithmData getIAlgorithmData(Context context) throws NoSuchMethodException {
        if(iAlgorithmData == null) {
            try {
                Class<?> libClazz = loadDex(context).loadClass("com.hengyue.sisensing.AlgorithmSplitData");
                iAlgorithmData = (IAlgorithmData) libClazz.newInstance();
            } catch (Exception e) {
                e.printStackTrace();
                Log.d(TAG, "getMethod error: " + e.getMessage());
            }
        }
        return iAlgorithmData;
    }

    /**
     * 加载dex文件中的class，并调用其中的方法
     * 这里由于是加载 jar文件，所以采用DexClassLoader
     * 下面开始加载dex class
     */
    public static ISplitAlgorithm getISplitAlgorithm(Context context) throws NoSuchMethodException {
        if(iSplitAlgorithm == null) {
            try {
                Class<?> libClazz = loadECODex(context).loadClass("com.hengyue.sisensingsplit.AlgorithmFactory");
                Method method = libClazz.getMethod("createAlgorithm", String.class);
                iSplitAlgorithm = (ISplitAlgorithm) method.invoke(null, "ALGORITHM E1.1.2G(2024_05_15)");
            } catch (Exception e) {
                e.printStackTrace();
                Log.d(TAG, "getMethod error: " + e.getMessage());
            }
        }
        return iSplitAlgorithm;
    }

    public static ISplitAlgorithmData getISplitAlgorithmData(Context context) throws NoSuchMethodException {
        if(iSplitAlgorithmData == null) {
            try {
                Class<?> libClazz = loadECODex(context).loadClass("com.hengyue.sisensingsplit.AlgorithmSplitData");
                iSplitAlgorithmData = (ISplitAlgorithmData) libClazz.newInstance();
            } catch (Exception e) {
                e.printStackTrace();
                Log.d(TAG, "getMethod error: " + e.getMessage());
            }
        }
        return iSplitAlgorithmData;
    }

    private static DexClassLoader loadDex(Context context){
        loadSoType = 1;
        if (dexClassLoader == null) {
            File cacheFile = context.getCacheDir();
            Log.d(TAG, "loadDexClass file path: " + cacheFile.getAbsolutePath());
            String internalPath = cacheFile.getAbsolutePath() + File.separator + "si.dex";
            File desFile = new File(internalPath);
            if (desFile.exists()) {
                dexClassLoader = new DexClassLoader(internalPath, cacheFile.getAbsolutePath(), cacheFile.getAbsolutePath(), context.getClass().getClassLoader());
            } else {
                Log.d(TAG, "dex not exist!!!");
            }
        }
        return dexClassLoader;
    }

    private static DexClassLoader loadECODex(Context context) {
        loadSoType = 2;
        if (ecoDexClassLoader == null) {
            File cacheFile = context.getCacheDir();
            String internalPath = cacheFile.getAbsolutePath() + File.separator + "eco" + File.separator + "eco.dex";
            String dirPath = cacheFile.getAbsolutePath() + File.separator + "eco";
            Log.d(TAG, "loadDexClass file path: " + internalPath);
            File desFile = new File(internalPath);
            if (desFile.exists()) {
                ecoDexClassLoader = new DexClassLoader(internalPath, dirPath, dirPath, context.getClass().getClassLoader());
            } else {
                Log.d(TAG, "dex not exist!!!");
            }
        }
        return ecoDexClassLoader;
    }
}
