package com.hengyue.utils;

import java.io.ByteArrayOutputStream;

public class ByteUtils {

    public static byte[] hexStringToIntArray(String hexString) {
        int length = hexString.length();
        // 创建一个整数数组
        byte[] data = new byte[length / 2];
        for (int i = 0; i < length; i += 2) {
            // 解析每两个字符为十六进制整数
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }

    public static String byteArrayToHexString(byte[] bytes) {
        if (bytes == null) {
            return null;
        }

        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(b & 0xFF); // Convert byte to hex
            if (hex.length() == 1) {
                hexString.append('0'); // Ensure two digits
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase(); // Convert to uppercase if needed
    }

    public static byte[] d(String str) {
        int i;
        int i2;
        int i3;
        int i4;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        for (int i5 = 0; i5 < str.length(); i5 += 2) {
            try {
                char charAt = str.charAt(i5);
                int i6 = i5 + 1;
                if (i6 < str.length()) {
                    char charAt2 = str.charAt(i6);
                    if (charAt < '0' || charAt > '9') {
                        if (charAt >= 'a' && charAt <= 'f') {
                            i = charAt - 'a';
                        } else {
                            if (charAt < 'A' || charAt > 'F') {
                                throw new IllegalArgumentException("hexUtil.bad");
                            }
                            i = charAt - 'A';
                        }
                        i2 = i + 10;
                    } else {
                        i2 = charAt - '0';
                    }
                    byte b = (byte) ((i2 * 16) + 0);
                    if (charAt2 < '0' || charAt2 > '9') {
                        if (charAt2 >= 'a' && charAt2 <= 'f') {
                            i3 = charAt2 - 'a';
                        } else {
                            if (charAt2 < 'A' || charAt2 > 'F') {
                                throw new IllegalArgumentException("hexUtil.bad");
                            }
                            i3 = charAt2 - 'A';
                        }
                        i4 = i3 + 10;
                    } else {
                        i4 = charAt2 - '0';
                    }
                    byteArrayOutputStream.write((byte) (b + i4));
                } else {
                    throw new IllegalArgumentException("hexUtil.odd");
                }
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return byteArrayOutputStream.toByteArray();
    }
}
