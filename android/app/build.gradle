plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.yiankang.wanyisheng"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "25.1.8937393"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    packagingOptions {
        resources {
            excludes += ['META-INF/INDEX.LIST', 'META-INF/io.netty.versions.properties','/META-INF/{AL2.0,LGPL2.1}','/META-INF/DEPENDENCIES']
        }
    }

    defaultConfig {
        // Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.yiankang.wanyisheng"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion 26 // flutter.minSdkVersion
        targetSdkVersion 33 // flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        manifestPlaceholders["JPUSH_PKGNAME"] = applicationId
        manifestPlaceholders["JPUSH_APPKEY"] = "9b1dfb0fafc450b1038fedd5"
        manifestPlaceholders["JPUSH_CHANNEL"] = "developer-default"

        manifestPlaceholders["XIAOMI_APPKEY"] = "MI-5912031713816"
        manifestPlaceholders["XIAOMI_APPID"] = "MI-2882303761520317816"

        manifestPlaceholders["HONOR_APPID"] = "900882666"

        manifestPlaceholders["VIVO_APPKEY"] = "fdc5e89ea64f7fabb9b6915f618cf97b"
        manifestPlaceholders["VIVO_APPID"] = "105792037"

        manifestPlaceholders["OPPO_APPKEY"] = "OP-7e7d002bbc2446898fe38fba195a0bef"
        manifestPlaceholders["OPPO_APPID"] = "***********"
        manifestPlaceholders["OPPO_APPSECRET"] = "OP-fd77aa5d3ccd462ab518e673f568f01c"

        manifestPlaceholders["MEIZU_APPKEY"] = "MZ-c18135e458fd49e1b4501cbdf62e0e2c"
        manifestPlaceholders["MEIZU_APPID"] = "MZ-154412"
        multiDexEnabled true

        manifestPlaceholders["JPUSH_PKGNAME"] = applicationId
        manifestPlaceholders["JPUSH_APPKEY"] = "fc3b4c6b50d481dd26564404"
        manifestPlaceholders["JPUSH_CHANNEL"] = "developer-default"
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            ndkConfig.abiFilters = ['arm64-v8a']
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            zipAlignEnabled true
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
        }
        debug {
            signingConfig signingConfigs.release
            ndkConfig.abiFilters = ['arm64-v8a', 'x86']
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            zipAlignEnabled true
            debuggable true
            jniDebuggable false
            renderscriptDebuggable false
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation files('libs/TDWearKit_release_1.0.1.133_051417.jar')
    implementation(files('libs/TDWearServiceSDK_release_1.0.1.133_051417.aar'))
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation'androidx.appcompat:appcompat:1.1.0'
    // network
    api 'com.squareup.retrofit2:retrofit:2.3.0'//导入retrofit
    api 'com.squareup.retrofit2:converter-gson:2.3.0'
    api 'com.squareup.okhttp3:logging-interceptor:4.9.1'
    // mqtt
    implementation 'com.hivemq:hivemq-mqtt-client:1.3.3'
    implementation 'org.apache.logging.log4j:log4j-core:2.16.0'

    //厂商推送
    // oppo
    implementation files('libs/com.heytap.msp_V3.5.3.aar')
    // 接入魅族厂商
    implementation files('libs/push-internal-5.0.3.aar')
    implementation 'cn.jiguang.sdk.plugin:meizu:5.7.0'

    // 接入小米厂商
    implementation 'cn.jiguang.sdk.plugin:xiaomi:5.7.0'

    // 接入华为厂商
    implementation 'com.huawei.hms:push:6.13.0.300'
    implementation 'cn.jiguang.sdk.plugin:huawei:5.7.0'// 极光厂商插件版本与接入 JPush 版本保持一致，下同

    // 接入荣耀厂商
    implementation 'com.hihonor.mcs:push:8.0.12.307'
    implementation 'cn.jiguang.sdk.plugin:honor:5.7.0'

    // vivo 通道集成指南
    implementation 'cn.jiguang.sdk.plugin:vivo:5.7.0'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

    implementation('com.amap.api:location:5.6.1')
}