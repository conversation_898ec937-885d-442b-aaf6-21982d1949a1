package com.yiankang.wanyisheng.util;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.util.Log;

import java.lang.reflect.Method;

public class BluetoothUtils {

    private static final String TAG = "BluetoothUtils";

    /**
     * 执行解除配对  反射
     * @param bluetoothDevice 蓝牙设备
     * @return  true 执行解绑  false未执行解绑
     */
    public boolean disBoundDevice(BluetoothDevice bluetoothDevice){
        if(bluetoothDevice == null){
            Log.e(TAG,"disBoundDevice-->bluetoothDevice == null");
            return false;
        }

        try {
            return removeBond(BluetoothDevice.class,bluetoothDevice);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 与设备解除配对 参考源码：platform/packages/apps/Settings.git
     * /Settings/src/com/android/settings/bluetooth/CachedBluetoothDevice.java
     */
    @SuppressWarnings("unchecked")
    static public boolean removeBond(Class btClass, BluetoothDevice btDevice)
            throws Exception {
        Method removeBondMethod = btClass.getMethod("removeBond");
        Boolean returnValue = (Boolean) removeBondMethod.invoke(btDevice);
        return returnValue.booleanValue();
    }

    public static void removeBond(String deviceAddress) {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceAddress);
        try {
            Method removeBondMethod = BluetoothDevice.class.getMethod("removeBond");
            boolean result = (Boolean) removeBondMethod.invoke(device);
            if (result) {
                // 移除成功
            } else {
                // 移除失败
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 移除失败
        }
    }
}
