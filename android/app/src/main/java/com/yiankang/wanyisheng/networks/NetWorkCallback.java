package com.yiankang.wanyisheng.networks;

import android.net.ConnectivityManager;
import android.net.LinkProperties;
import android.net.Network;
import android.net.NetworkCapabilities;

import androidx.annotation.NonNull;

import com.yiankang.wanyisheng.CustomApplication;


public class NetWorkCallback extends ConnectivityManager.NetworkCallback {

    private static final String TAG = "NetWorkCallback";

    @Override
    public void onAvailable(@NonNull Network network) {
        super.onAvailable(network);
        CustomApplication.Companion.instance().logMessage(TAG + " onAvailable");
        CustomApplication.Companion.instance().screenOn();
    }

    @Override
    public void onLost(@NonNull Network network) {
        super.onLost(network);
        CustomApplication.Companion.instance().logMessage(TAG + " onLost");
    }

    @Override
    public void onLosing(@NonNull Network network, int maxMsToLive) {
        super.onLosing(network, maxMsToLive);
    }

    @Override
    public void onUnavailable() {
        super.onUnavailable();
    }

    @Override
    //当网络状态修改（网络依然可用）时调用
    public void onCapabilitiesChanged(@NonNull Network network, @NonNull NetworkCapabilities networkCapabilities) {
        super.onCapabilitiesChanged(network, networkCapabilities);
    }

    @Override
    public void onBlockedStatusChanged(@NonNull Network network, boolean blocked) {
        super.onBlockedStatusChanged(network, blocked);
    }

    @Override
    public void onLinkPropertiesChanged(@NonNull Network network, @NonNull LinkProperties linkProperties) {
        super.onLinkPropertiesChanged(network, linkProperties);
    }
}
