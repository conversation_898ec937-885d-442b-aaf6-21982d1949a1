package com.yiankang.wanyisheng.networks;

import android.content.Context;

import androidx.annotation.NonNull;


import com.yiankang.wanyisheng.CustomApplication;
import com.yiankang.wanyisheng.networks.status.NetWorkUtils;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.flutter.plugin.common.MethodChannel;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class NetworkManager {

    private static final MediaType JSON = MediaType.get("application/json");
    private static final int connectTimeoutInSeconds = 30;
    private static final int readTimeoutInSeconds = 30;
    private static final int writeTimeoutInSeconds = 30;

    private final OkHttpClient client;

    private static class InstanceHolder {
        private static final NetworkManager INSTANCE = new NetworkManager();
    }

    public static NetworkManager getInstance(){
        return InstanceHolder.INSTANCE;
    }

    private NetworkManager() {
        OkHttpClient.Builder httpBuilder = new OkHttpClient.Builder();
//                .dns(new XDns(timeoutInSeconds()))
        List<Interceptor> interceptors = interceptors();
        if (interceptors != null && !interceptors.isEmpty()) {
            for (Interceptor interceptor : interceptors) {
                httpBuilder.addInterceptor(interceptor);
            }
        }
        configOkHttp(httpBuilder);
        client = httpBuilder.build();
    }

    private OkHttpClient.Builder configOkHttp(OkHttpClient.Builder builder) {
        builder.sslSocketFactory(new SSL(SSL.getX509TrustManager()), SSL.getX509TrustManager())
                .connectTimeout(connectTimeoutInSeconds, TimeUnit.SECONDS)
//                .connectionPool(new ConnectionPool(0, 0, TimeUnit.SECONDS))
                .readTimeout(readTimeoutInSeconds, TimeUnit.SECONDS)
                .writeTimeout(writeTimeoutInSeconds, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true);
        return builder;
    }

    private List<Interceptor> interceptors() {
        List<Interceptor> interceptors = new ArrayList<>();
//        if (AppContext.getInstance().isDebugMode()) {
//            HttpLoggingInterceptor logger = new HttpLoggingInterceptor();
//            logger.setLevel(HttpLoggingInterceptor.Level.HEADERS).setLevel(HttpLoggingInterceptor.Level.BODY);
//            interceptors.add(logger);
//        }

        return interceptors;
    }

    public void newCall(String url, Map<String, Object>  headrs, String json, Callback callback) {
        if (client != null) {
            RequestBody body = RequestBody.create(json, JSON);
            Request.Builder builder = new Request.Builder();
            builder.url(url);
            if (headrs != null) {
                for (String key : headrs.keySet()) {
                    builder.addHeader(key, headrs.get(key).toString());
                }
            }
            builder.post(body);
            client.newCall(builder.build()).enqueue(callback);
        }
    }

    public void newCall(Context context,@NotNull String url, @NotNull Map<String, Object> headrs, @NotNull String json, @NotNull MethodChannel.Result result) {
        if (NetWorkUtils.isNetAvailable(context)) {
            newCall(url, headrs, json, new Callback() {
                @Override
                public void onFailure(@NonNull Call call, @NonNull IOException e) {
                    ((CustomApplication) context).logMessage("网络失败错误" + e.getMessage());
                    result.success("");
                }

                @Override
                public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                    String body = response.body().string();
                    ((CustomApplication) context).logMessage(body);
                    result.success(body);
                }
            });
        }else {
            ((CustomApplication) context).logMessage("没有网络");
            result.success("");
        }
    }
}
