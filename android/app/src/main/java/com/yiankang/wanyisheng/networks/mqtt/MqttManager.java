package com.yiankang.wanyisheng.networks.mqtt;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hivemq.client.mqtt.datatypes.MqttQos;
import com.hivemq.client.mqtt.lifecycle.MqttClientConnectedContext;
import com.hivemq.client.mqtt.lifecycle.MqttClientConnectedListener;
import com.hivemq.client.mqtt.lifecycle.MqttClientDisconnectedContext;
import com.hivemq.client.mqtt.lifecycle.MqttClientDisconnectedListener;
import com.hivemq.client.mqtt.mqtt3.Mqtt3AsyncClient;
import com.hivemq.client.mqtt.mqtt3.Mqtt3Client;
import com.hivemq.client.mqtt.mqtt3.message.connect.connack.Mqtt3ConnAck;
import com.hivemq.client.mqtt.mqtt3.message.publish.Mqtt3Publish;
import com.hivemq.client.mqtt.mqtt3.message.subscribe.suback.Mqtt3SubAck;
import com.yiankang.wanyisheng.CustomApplication;
import com.yiankang.wanyisheng.networks.NetworkManager;
import com.yiankang.wanyisheng.networks.status.NetWorkUtils;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import io.flutter.plugin.common.MethodChannel;
import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

public class MqttManager implements MqttClientConnectedListener, MqttClientDisconnectedListener {

    private static final String TAG = "MqttManager ";

//    private static final String serverURIDEV = "emq-broker-dev.medhy.cn";
//    private static final String serverURI = "emq-broker-dev.medhy.cn";
    private static final int port = 1883;

    private static class InstanceHolder {
        private static final MqttManager INSTANCE = new MqttManager();
    }

    public static MqttManager getInstance() {
        return InstanceHolder.INSTANCE;
    }

    private MqttManager() {}

    private Mqtt3AsyncClient mqttClient;
    private boolean isConnected = false;

    private String userTopic;

    private String serverURI = "";

    private long expire;
    private String mqttToken;


    public void setServerURI(String serverURI) {
        this.serverURI = serverURI;
    }

    public void initMqtt(String from) {
        synchronized (MqttManager.class) {
            CustomApplication.Companion.instance().isLogin(new MethodChannel.Result() {
                @Override
                public void success(@Nullable Object result) {
                    if (result instanceof Boolean && ((Boolean) result).booleanValue()) {
                        mqttAuth();
                    } else {
                        CustomApplication.Companion.instance().logMessage(TAG + " initMqtt is fail user is not login");
                    }
                }

                @Override
                public void error(@NonNull String errorCode, @Nullable String errorMessage, @Nullable Object errorDetails) {
                    CustomApplication.Companion.instance().logMessage(TAG + " initMqtt is fail method channel error errorMessage is " + errorMessage);
                }

                @Override
                public void notImplemented() {
                    CustomApplication.Companion.instance().logMessage(TAG + " initMqtt is fail method channel enotImplemented ");
                }
            });
        }
    }

    private synchronized void mqttAuth(){
        if (mqttClient != null && isConnected) {
            CustomApplication.Companion.instance().logMessage(TAG + " initMqtt is connected");
            if (!TextUtils.isEmpty(userTopic)) {
                sendMessage(userTopic, "pong");
            }
        } else {
//            {"url":"https://ytj-api-dev.medhy.cn/hengyue-api/app/mqtt/getEmqToken","body":"eyJyZXF1ZXN0VGltZXN0YW1wIjoiMjAyNS0wMi0yNiAxNzowMTowNCJ9","headers":{"os":"android","deviceId":"416b0694fda618abf909594c9b5df929","channel":"","req":"eb27c9e092e078f8387e27675270b3b99f35e880ff2bac0cb65af068bae0874b","version":"202502240","timezone":"8","lang":"zh_CN","device-info":"{\"screenWidth\":375.0,\"screenHeight\":812.0,\"model\":\"MI 8\",\"brand\":\"Xiaomi\",\"name\":\"dipper\",\"version\":\"29\",\"isPhysicalDevice\":true}","token":"abac521866164c108916cfc4ee8f084d","content-type":"application/json"}}
            CustomApplication.Companion.instance().logMessage(TAG + "expire = " + expire +" mqttTokenResult = " + mqttToken);
            // 有效期缩短一分钟防止 网络请求延迟原因验证失败
            if (!TextUtils.isEmpty(mqttToken) && (expire - 60 * 1000) > System.currentTimeMillis()) {
                connectMqtt(mqttToken);
            } else {
                CustomApplication.Companion.instance().getMqttTokenParams(new MethodChannel.Result() {
                    @Override
                    public void success(@Nullable Object result) {
                        if (!TextUtils.isEmpty((CharSequence) result)) {
                            try {
                                JSONObject jsonObject = new JSONObject(result.toString());
                                String url = jsonObject.getString("url");
                                String body = jsonObject.getString("body");
                                JSONObject headerJson = jsonObject.getJSONObject("headers");
                                Map<String, Object> headers = new HashMap<>();
                                Iterator<String> keys = headerJson.keys();
                                while (keys.hasNext()) {
                                    String key = keys.next();
                                    headers.put(key, headerJson.get(key));
                                }
                                NetworkManager.getInstance().newCall(url, headers, body, new Callback() {
                                    @Override
                                    public void onFailure(@NonNull Call call, @NonNull IOException e) {
                                        CustomApplication.Companion.instance().logMessage(TAG + "okhttp onFailure " + e.getMessage());
                                    }

                                    @Override
                                    public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                                        String responesStr = response.body().string();
                                        CustomApplication.Companion.instance().logMessage(TAG + "okhttp onResponse " + responesStr);
                                        connectMqtt(responesStr);
                                    }
                                });
                            } catch (JSONException e) {
                                throw new RuntimeException(e);
                            }
                        } else {
                            CustomApplication.Companion.instance().logMessage(TAG + "result is null");
                        }
                    }

                    @Override
                    public void error(@NonNull String errorCode, @Nullable String errorMessage, @Nullable Object errorDetails) {
                        CustomApplication.Companion.instance().logMessage(TAG + "MethodChannel is error " + errorMessage);
                    }

                    @Override
                    public void notImplemented() {
                        CustomApplication.Companion.instance().logMessage(TAG + "MethodChannel is notImplemented");
                    }
                });
            }
        }
    }

    public void connectMqtt(String result){
//        initMqtt{"msg":"操作成功","code":0,"data":{"clientId":"mqtt_app_1773184411327217666","publicTopic":"app/public/ping","topic":"appUser/event/1773184411327217666","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJtcXR0X2FwcF8xNzczMTg0NDExMzI3MjE3NjY2IiwiZXhwIjoxNzQwNTYyODY0fQ.x1lYosrNuGNrH7bq9KMeUuNwRZkDY_9T9AnIl7ZcWXM"}}
//        {"code":0,"data":{"clientId":"mqtt_app_1937081292604235777","publicTopic":"","topic":"appUser/event/1937081292604235777","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJtcXR0X2FwcF8xOTM3MDgxMjkyNjA0MjM1Nzc3IiwiZXhwIjoxNzUyMDQ3NTI5fQ.fgOnZAUNOP0LA3ITCQSL--ut5n5mQVNobxxnguCeImQ","planToken":null,"expire":1752047529186},"msg":"操作成功"}
        CustomApplication.Companion.instance().logMessage(TAG + "connectMqtt serverURI = " + serverURI);
        CustomApplication.Companion.instance().logMessage(TAG + "connectMqtt result = " + result);
        try {
            JSONObject jsonObject = new JSONObject(String.valueOf(result));
            JSONObject dataJson = jsonObject.getJSONObject("data");
            String clientId = dataJson.getString("clientId");
            userTopic = dataJson.getString("topic");
            String token = dataJson.getString("token");
            if(dataJson.has("expire")){
                expire = dataJson.getLong("expire");
                mqttToken = result;
            }
            CustomApplication.Companion.instance().logMessage(TAG + "connectMqtt = " + serverURI + " port " + port + " token " + token);
            mqttClient = Mqtt3Client.builder()
                    .identifier(clientId)
                    .serverHost(serverURI)
                    .serverPort(port)
                    .addConnectedListener(MqttManager.this)
                    .addDisconnectedListener(MqttManager.this)
                    .simpleAuth()
                    .username(clientId)
                    .password(token.getBytes())
                    .applySimpleAuth()
                    .buildAsync();
            mqttClient.connectWith()
                    .cleanSession(true)
                    .keepAlive(30*60)
                    .send()
                    .whenComplete(new BiConsumer<Mqtt3ConnAck, Throwable>() {
                        @Override
                        public void accept(Mqtt3ConnAck mqtt3ConnAck, Throwable throwable) {
                            if (mqtt3ConnAck != null) {
                                CustomApplication.Companion.instance().logMessage(TAG + "链接成功");
                                isConnected = true;
                                mqttClient.subscribeWith().topicFilter(userTopic).callback(new Consumer<Mqtt3Publish>() {
                                    @Override
                                    public void accept(Mqtt3Publish mqtt3Publish) {
                                        String msg = new String(mqtt3Publish.getPayloadAsBytes());
                                        CustomApplication.Companion.instance().logMessage(TAG + "mqtt_收到消息 : topic = " + mqtt3Publish.getTopic() + msg);
                                        if ("ping".equals(msg)) {
                                            sendMessage("/listener", "pong");
                                        } else if ("pong".equals(msg)) {
                                            CustomApplication.Companion.instance().logMessage("mqtt_收到消息 : topic = " + mqtt3Publish.getTopic());
                                        } else {
                                            doAction(msg);
                                        }
                                    }
                                }).send().whenComplete(new BiConsumer<Mqtt3SubAck, Throwable>() {
                                    @Override
                                    public void accept(Mqtt3SubAck mqtt3SubAck, Throwable throwable) {
                                        if (throwable != null) {
                                            CustomApplication.Companion.instance().logMessage(TAG + "订阅失败: " + throwable.getMessage());
                                        } else {
                                            CustomApplication.Companion.instance().logMessage(TAG + "成功订阅 " + userTopic);
                                        }
                                    }
                                });
                                sendMessage(userTopic,"pong");
                            } else if (throwable != null) {
                                isConnected = false;
                                CustomApplication.Companion.instance().logMessage(TAG + "链接失败" + throwable.getMessage());
                            }
                        }
                    });
            CustomApplication.Companion.instance().logMessage(TAG + "mqttClient connect");
        } catch (JSONException e) {
            CustomApplication.Companion.instance().logMessage(TAG + "JSONException");
        }
    }

    public void sendMessage(String topic, String content) {
        CustomApplication.Companion.instance().logMessage(TAG + "content =" + content);
        if (mqttClient != null && isConnected) {
            CustomApplication.Companion.instance().logMessage(TAG + "content = 1" + content);
            mqttClient.publishWith()
                    .topic(topic)
                    .qos(MqttQos.AT_MOST_ONCE)
                    .payload(content.getBytes())
                    .send()
                    .whenComplete(new BiConsumer<Mqtt3Publish, Throwable>() {
                        @Override
                        public void accept(Mqtt3Publish mqtt3Publish, Throwable throwable) {
                            if (throwable != null) {
                                CustomApplication.Companion.instance().logMessage(TAG + "发 失败: "+throwable.getMessage());
                            } else {
                                CustomApplication.Companion.instance().logMessage(TAG + "已发送: " + content);
                            }
                        }
                    });
        }else {
            CustomApplication.Companion.instance().logMessage(TAG + "mqttClient" + (mqttClient == null)  + " isConnected = " + isConnected);
        }
    }

    public void disconnect() {
        if (mqttClient != null && isConnected) {
           mqttClient.disconnect();
           isConnected = false;
        }
    }

    @Override
    public void onConnected(@NotNull MqttClientConnectedContext context) {
        isConnected = true;
        CustomApplication.Companion.instance().logMessage(TAG + "mqttClient onConnected");
    }

    @Override
    public void onDisconnected(@NotNull MqttClientDisconnectedContext context) {
        isConnected = false;
        CustomApplication.Companion.instance().logMessage(TAG + "mqttClient onDisconnected");

        Observable.just(1).delay(30, TimeUnit.SECONDS).subscribe(new Observer<Integer>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(Integer integer) {
                CustomApplication.Companion.instance().logMessage(TAG + "onNext");
                if (NetWorkUtils.isNetAvailable(CustomApplication.Companion.instance())) {
                    initMqtt("onDisconnected");
                }
            }

            @Override
            public void onError(Throwable e) {
                CustomApplication.Companion.instance().logMessage(TAG + "onError");
            }

            @Override
            public void onComplete() {
                CustomApplication.Companion.instance().logMessage(TAG + "onComplete");
            }
        });
    }


    private void doAction(String event) {
        CustomApplication.Companion.instance().mqttDoAction(event);
    }
}
