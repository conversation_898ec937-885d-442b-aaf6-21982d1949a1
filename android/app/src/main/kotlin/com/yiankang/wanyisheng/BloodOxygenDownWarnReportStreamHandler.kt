package com.yiankang.wanyisheng

import android.content.Context
import io.flutter.plugin.common.EventChannel
import com.tdtech.iwear.listener.ResultCallback
import com.tdtech.iwearkit.data.store.HiHealthDataStore
import com.tdtech.iwearkit.data.store.HiRealTimeListener

/**
 * 血氧告警事件流处理器
 * 通过EventChannel向Flutter端推送onResult和onChange事件
 */
class BloodOxygenDownWarnReportStreamHandler(private val context: Context) : EventChannel.StreamHandler {
    private var eventSink: EventChannel.EventSink? = null
    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        HiHealthDataStore.openBloodOxygenDownReport(context, object : HiRealTimeListener {
            override fun onResult(state: Int) {
                val map = hashMapOf<String, Any?>("event" to "onResult", "state" to state)
                eventSink?.success(map)
            }
            override fun onChange(resultCode: Int, value: String?) {
                val map = hashMapOf<String, Any?>("event" to "onChange", "resultCode" to resultCode, "value" to value)
                eventSink?.success(map)
            }
        })
    }
    override fun onCancel(arguments: Any?) {
        eventSink = null
        // 取消流时自动关闭血氧告警上报
        HiHealthDataStore.closeBloodOxygenDownReport(
            context,
            object : ResultCallback {
                override fun onResult(resultCode: Int, message: Any?) {
                    // 可选：可在此记录日志或处理回调
                }
            }
        )
    }
} 