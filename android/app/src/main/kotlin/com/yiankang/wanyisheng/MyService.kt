package com.yiankang.wanyisheng

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import com.yiankang.wanyisheng.networks.mqtt.MqttManager
import java.text.SimpleDateFormat
import java.util.Date


class MyService: Service() {

    companion object {
        const val CHANNEL_ID = "ForegroundServiceChannel"
        const val CHANNEL_NAME = "Foreground Service"
        const val NOTIFICATION_ID = 1001
        var EXECUTE_COUNT= -1

        // 启动服务的便捷方法
        fun startService(context: Context) {
            val intent = Intent(context, MyService::class.java).apply {
                putExtra("START_SERVICE", "")
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        // 启动服务的便捷方法
        fun startWorkTask(context: Context) {
            val intent = Intent(context, MyService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        // 停止服务的便捷方法
        fun stopService(context: Context) {
            val intent = Intent(context, MyService::class.java)
            context.stopService(intent)
        }

        // 新增静态更新方法
        fun updateNotificationText(context: Context, text: String) {
            val updateIntent = Intent(context, MyService::class.java).apply {
                putExtra("UPDATE_TEXT", text)
                action = "ACTION_UPDATE_NOTIFICATION"
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(updateIntent)
            } else {
                context.startService(updateIntent)
            }
        }
    }

    private val notificationManager by lazy {
        getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }


    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.service_notification_description)
            }
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun buildNotification(): Notification {
        // 通知点击的PendingIntent
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.service_notification_title))
            .setContentText(getString(R.string.service_notification_content_default))
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()
    }

    // 添加更新通知的方法
    fun updateNotification(contentText: String) {
        val updatedNotification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.service_notification_title))
            .setContentText(contentText)  // 动态文本
            .setSmallIcon(R.mipmap.ic_launcher)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE)
                as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, updatedNotification)
    }


    @SuppressLint("InvalidWakeLockTag")
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        // 创建并显示通知
        val notification = buildNotification()
        // 启动前台服务（需要唯一通知ID）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                NOTIFICATION_ID,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
            )
        }else{
            startForeground(
                NOTIFICATION_ID,
                notification)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if(intent?.hasExtra("START_SERVICE") == true){
            CustomApplication.instance().logMessage("onStartCommand = start service " )
            return  START_STICKY
        }
        if(intent?.hasExtra("UPDATE_TEXT") == true) {
            CustomApplication.instance().logMessage("onStartCommand = update notification " )
            // 处理更新请求
            intent.getStringExtra("UPDATE_TEXT")?.let { text ->
                updateNotification(text)
                return START_STICKY
            }
        }
        CustomApplication.instance().startWorkTask()
        // 处理30分钟唤醒
        val sdf = SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss")
        CustomApplication.instance().logMessage("onStartCommand = " + sdf.format(Date(System.currentTimeMillis())))
//        (applicationContext as CustomApplication).nativePrepareAtBeginOfFiveMinPoll()   // 这里申请了一个30s的唤醒锁, 不申请唤醒锁，可能蓝牙等都有问题
        MqttManager.getInstance().initMqtt("myService")
        EXECUTE_COUNT ++
        if (EXECUTE_COUNT % 6 != 0) return START_STICKY
        (applicationContext as CustomApplication).methodChannel?.let {
            CustomApplication.instance().logMessage("onStartCommand")
            it.invokeMethod("reportLocation", "")
        }
        return START_STICKY
    }

}