package com.yiankang.wanyisheng

import android.content.Context
import com.tdtech.iwear.TdApiConstants
import io.flutter.plugin.common.EventChannel
import com.tdtech.iwear.listener.ResultCallback
import com.tdtech.iwearkit.data.store.HiHealthDataStore
import org.json.JSONObject

class StressDataStreamHandler(private val context: Context) : EventChannel.StreamHandler {
    private var eventSink: EventChannel.EventSink? = null
    private var isSubscribed = false
    private var devMac: String? = null

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        val devMac = (arguments as? Map<*, *>)?.get("devMac") as? String
        if (devMac == null) {
            eventSink?.error("INVALID_ARGUMENT", "devMac is null", null)
            return
        }
        try {
            val jsonObject = JSONObject()
            jsonObject.put(TdApiConstants.QUERY_ITEM, TdApiConstants.STRESS_DATA)
            val jsonParam = jsonObject.toString()
            HiHealthDataStore.subscribe(context, devMac, jsonParam, object : ResultCallback {
                override fun onResult(code: Int, data: Any?) {
                    val map = hashMapOf<String, Any?>(
                        "code" to code,
                        "data" to data
                    )
                    eventSink?.success(map)
                }
            })
            isSubscribed = true
        } catch (e: Exception) {
            eventSink?.error("JSON_ERROR", e.message, null)
        }
    }

    override fun onCancel(arguments: Any?) {
        // 取消原生压力数据订阅
        if (devMac != null && isSubscribed) {
            try {
                val jsonObject = JSONObject()
                jsonObject.put(TdApiConstants.QUERY_ITEM, TdApiConstants.STRESS_DATA)
                val jsonParam = jsonObject.toString()
                HiHealthDataStore.unsubscribe(context, devMac, jsonParam, object : ResultCallback {
                    override fun onResult(code: Int, data: Any?) {
                        // 可选：可以通过日志或回调通知取消结果
                    }
                })
            } catch (e: Exception) {
                // 可选：处理异常
            }
        }
        eventSink = null
        isSubscribed = false
    }
} 