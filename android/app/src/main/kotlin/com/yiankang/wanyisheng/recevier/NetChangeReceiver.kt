package com.yiankang.wanyisheng.recevier

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.yiankang.wanyisheng.CustomApplication

class NetChangeReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        Log.e("HiHealthKit_Test", "~~~~~~~~~~~onReceive~~~~~~~~~")
        if (intent != null) {
            val state = intent.getIntExtra("state", -1)
            val devName = intent.getStringExtra("devName") ?: ""
            Log.e("HiHealthKit_Test", "state: $state, devName: $devName")
            // 通过 Application 获取 healthSdkChannel
            val app = context.applicationContext as? CustomApplication
            val channel = app?.healthSdkChannel
            channel?.invokeMethod(
                "onConnectionStateChanged",
                mapOf(
                    "state" to state,
                    "devName" to devName
                )
            )
        }
    }
}