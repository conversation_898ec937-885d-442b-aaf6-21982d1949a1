package com.yiankang.wanyisheng

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.app.AlarmManager
import android.app.PendingIntent
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.net.Uri
import android.net.wifi.WifiManager
import android.os.Build
import android.os.PowerManager
import android.os.Vibrator
import android.provider.Settings
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.tdtech.tdcommonmodel.application.BaseApplication
import com.tdtech.tdlogsmodel.TdLogUtil
import com.yiankang.wanyisheng.networks.NetworkManager
import com.yiankang.wanyisheng.networks.mqtt.MqttManager
import com.yiankang.wanyisheng.networks.status.NetWorkUtils
import com.yiankang.wanyisheng.recevier.NetChangeReceiver
import com.yiankang.wanyisheng.util.BluetoothUtils
import io.flutter.app.FlutterApplication
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import io.reactivex.plugins.RxJavaPlugins
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.properties.Delegates

class CustomApplication : BaseApplication(), MethodChannel.MethodCallHandler {

    private val TAG = "CustomApplication"

    private val methodChannelName = "com.yiankang.wanyisheng/channel"
    private val healthSdkChannelName = "com.yiankang.health_sdk/channel"

    companion object {
        const val FLUTTER_ENGINE_CACHE_ID: String = "my_engine_id"
        private var instance: CustomApplication by Delegates.notNull()
        fun instance() = instance
    }

    private val alarmInterval = 5 * 60 * 1000
    var methodChannel: MethodChannel? = null
    var healthSdkChannel: MethodChannel? = null
    private var pendingIntent: PendingIntent? = null
    private var valueView: TextView? = null
    private var floatLayout: LinearLayout? = null
    private var trendView: TextView? = null
    private var timeView: TextView? = null
    private val simpleDateFormat: SimpleDateFormat = SimpleDateFormat("HH:mm")
    private var wakeLock: PowerManager.WakeLock? = null
    private var healthSdkHandler: HealthSdkMethodCallHandler? = null
    private var netChangeReceiver: NetChangeReceiver? = null
    private var watchButtonEventReceiver: WatchButtonEventReceiver? = null

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    override fun onCreate() {
        super.onCreate()
        instance = this
        var processName = getProcessName(this);
        if (packageName.equals(processName)) {
            initFlutterEngine()
        }
        RxJavaPlugins.setErrorHandler { e ->
            e.printStackTrace()
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun logMessage(msg: String) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel?.invokeMethod("log", msg)
        }
    }

    fun isLogin(callback: MethodChannel.Result) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel?.invokeMethod("isLogin", "", callback)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun getMqttTokenParams(callback: MethodChannel.Result) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel?.invokeMethod("mqtt_token", "", callback)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun mqttDoAction(msg: String) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel?.invokeMethod("mqtt_do_action", msg)
        }
    }

    fun getProcessName(context: Context): String? {
        var pid = android.os.Process.myPid();
        var manager: ActivityManager =
            context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        for (processInfo in manager.runningAppProcesses) {
            if (processInfo.pid == pid) {
                return processInfo.processName
            }
        }
        return null;
    }

    fun initFlutterEngine() {
        val flutterEngine = FlutterEngine(this);
        flutterEngine.let {
            // Start executing Dart code to pre-warm the FlutterEngine.
            it.dartExecutor.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            );
            GeneratedPluginRegistrant.registerWith(flutterEngine)
            // Cache the FlutterEngine to be used by FlutterActivity.
            FlutterEngineCache
                .getInstance()
                .put(FLUTTER_ENGINE_CACHE_ID, it)
            initFlutterPlugin(it)
        }
    }

    private fun initFlutterPlugin(flutterEngine: FlutterEngine) {
        methodChannel = flutterEngine.dartExecutor.binaryMessenger.let {
            MethodChannel(it, methodChannelName)
        }
        methodChannel?.setMethodCallHandler(this)

        healthSdkHandler = HealthSdkMethodCallHandler(applicationContext)
        healthSdkChannel = flutterEngine.dartExecutor.binaryMessenger.let {
            MethodChannel(it, healthSdkChannelName)
        }
        healthSdkChannel?.setMethodCallHandler(healthSdkHandler)

        // 动态注册 NetChageReceive 广播接收器
        if (netChangeReceiver == null) {
            netChangeReceiver = NetChangeReceiver()
            val filter = IntentFilter("com.tdtech.wear.action.CONNECTION_STATE_CHANGED")
            registerReceiver(netChangeReceiver,filter,RECEIVER_EXPORTED)
        }
        if (watchButtonEventReceiver == null) {
            watchButtonEventReceiver = WatchButtonEventReceiver()
            val filter = IntentFilter("com.tdtech.wear.action.EVENT_REPORT")
            registerReceiver(
                watchButtonEventReceiver,
                filter,
                RECEIVER_EXPORTED
            )
        }
    }


    fun startWorkTask(interVal: Long = alarmInterval.toLong()) {
        logMessage("启动下一个五分钟, 间隔时间：$interVal")
        val sdf = SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss")
        val intent = Intent(this, MyService::class.java)
        val manager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        manager.let {
            logMessage("startWorkTask：" + sdf.format(Date(System.currentTimeMillis() + interVal)))
            if (pendingIntent != null) {
                try {
                    it.cancel(pendingIntent!!)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            pendingIntent = PendingIntent.getService(
                this,
                1,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            it.setAlarmClock(
                AlarmManager.AlarmClockInfo(
                    (System.currentTimeMillis() + interVal),
                    pendingIntent
                ), pendingIntent!!
            )
        }
    }

    fun stopWorkTask() {
        val manager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        pendingIntent?.let { manager.cancel(it) }
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        logMessage(TAG + "call method " + call.method)
        if ("start" == call.method) {
            logMessage("启动5分钟轮询")
            if (call.hasArgument("interval")) {
                val interval = call.argument("interval") ?: 0
                if (interval > 0) {
                    startWorkTask(interval.toLong())
                    return;
                }
            }
            startWorkTask()
        } else if ("stop" == call.method) {
            logMessage("取消5分钟轮询")
            stopWorkTask();
        } else if ("ring" == call.method) {
            startRing()
        } else if ("play_ring" == call.method) {
            playRing()
        } else if ("play_audio_ring" == call.method) {
            playRingByAudioRing()
        } else if ("play_ring_msg" == call.method) {
            playRingMsg()
        } else if ("play_audio_ring_msg" == call.method) {
            playRingByAudioRingMsg()
        } else if ("vibrate" == call.method) {
            startVibrate();
        } else if (call.method == "getDeviceID") {
            val deviceID = getDeviceID()

            if (deviceID != null) {
                result.success(deviceID)
            } else {
                result.error("UNAVAILABLE", "device id not available.", null)
            }
        } else if ("removeBond" == call.method) {
            if (call.hasArgument("device_address")) {
                val deviceAddress = call.argument<String>("device_address")
                BluetoothUtils.removeBond(deviceAddress)
            }
        } else if (call.method == "http_request") {
            val url = call.argument<String>("url").toString()
            val headers = call.argument<Map<String, Any>>("header")
            val body = call.argument<String>("body")
            if (url != null && headers != null && body != null) {
                NetworkManager.getInstance().newCall(this, url, headers, body, result)
            }
        } else if (call.method == "mqtt_init") {
            MqttManager.getInstance().initMqtt("mqtt_init")
        } else if (call.method == "mqtt_set_server_uri") {
            val url = call.argument<String>("url").toString()
            MqttManager.getInstance().setServerURI(url)
        } else if (call.method == "update_glucose_notification") {
            val calibratedValue = call.argument<String>("calibratedValue").toString()
            val trend = call.argument<String>("trend").toString()
            val recordTime = call.argument<String>("record_time").toString()
            MyService.updateNotificationText(
                this,
                getString(
                    R.string.service_notification_content,
                    simpleDateFormat.format(simpleDateFormat.parse(recordTime)),
                    calibratedValue,
                    getTrendSymbol(trend)
                )
            )
        } else if (call.method == "mqtt_send") {
            val topic = call.argument<String>("topic")
            val content = call.argument<String>("content")
            MqttManager.getInstance().sendMessage(topic, content)
        } else if (call.method == "background_location") {
            val isGranted = ContextCompat.checkSelfPermission(
                this,
                android.Manifest.permission.ACCESS_BACKGROUND_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
            logMessage("background_location isGranted = $isGranted")
            return result.success(isGranted)
        } else if (call.method == "isInstalled") {
            val packageName = call.argument<String>("package")!!
            val pm: PackageManager = applicationContext.packageManager
            val isInstalled = try {
                pm.getPackageInfo(packageName, 0)
                true
            } catch (e: PackageManager.NameNotFoundException) {
                false
            }
            result.success(isInstalled)
        } else if (call.method == "open_market") {
            val packageName = call.argument<String>("package")!!
            try {
                // 1. 优先尝试通用 market:// 协议（兼容Google Play和部分国内商店）
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse("market://details?id=$packageName")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                this.startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                try {
                    // 2. 如果 market:// 失败，尝试直接打开网页版（默认跳转应用宝）
                    val intent = Intent(Intent.ACTION_VIEW).apply {
                        data =
                            Uri.parse("https://a.app.qq.com/o/simple.jsp?pkgname=$packageName")
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    this.startActivity(intent)
                } catch (e: Exception) {
                    Toast.makeText(this, "无法跳转到应用商店", Toast.LENGTH_SHORT).show()
                }
            }
        } else {
            result.notImplemented()
        }
    }

    /**
     * 获取devid
     * @return
     */
    fun getDeviceID(): String? {
        try {
            // 2 compute DEVICE ID
            val devIDShort = "35" + // we make this look like a valid IMEI
                    Build.BOARD.length % 10 + Build.BRAND.length % 10 + Build.CPU_ABI.length % 10 + Build.DEVICE.length % 10 + Build.DISPLAY.length % 10 + Build.HOST.length % 10 + Build.ID.length % 10 + Build.MANUFACTURER.length % 10 + Build.MODEL.length % 10 + Build.PRODUCT.length % 10 + Build.TAGS.length % 10 + Build.TYPE.length % 10 + Build.USER.length % 10 // 13 digits

            // 3 android ID - unreliable
            val androidId: String = Settings.Secure.getString(
                this.getContentResolver(), Settings.Secure.ANDROID_ID
            )

            // 4 wifi manager read MAC address - requires
            // android.permission.ACCESS_WIFI_STATE or comes as null
            val wm: WifiManager = this.getApplicationContext()
                .getSystemService(Context.WIFI_SERVICE) as WifiManager
            var serial: String? = null
            if (wm != null) {
                try {
                    serial = Build.SERIAL
                } catch (_: Exception) {

                }
            }

            /*
         * BluetoothAdapter bluetoothAdapter = null; // Local Bluetooth
         * adapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
         * if (bluetoothAdapter != null) { try { btMac =
         * bluetoothAdapter.getAddress(); } catch (Exception e) {
         * Log.d("UUID", e.getMessage(), e); } }
         */

            // 6 SUM THE IDs
            val devIdLong = devIDShort + androidId + serial
            var m: MessageDigest? = null
            try {
                m = MessageDigest.getInstance("MD5")
            } catch (_: NoSuchAlgorithmException) {
            }
            m?.update(devIdLong.toByteArray(), 0, devIdLong.length)
            val md5Data: ByteArray? = m?.digest()
            var uniqueId = ""
            var i = 0
            val len = md5Data?.size ?: 0
            while (i < len) {
                val b = 0xFF and (md5Data?.get(i)?.toInt() ?: 0)
                // if it is a single digit, make sure it have 0 in front (proper
                // padding)
                if (b <= 0xF) uniqueId += "0"
                // add number to string
                uniqueId += Integer.toHexString(b)
                i++
            }
            uniqueId = uniqueId.toUpperCase()
            if (uniqueId.length > 15) {
                uniqueId = uniqueId.substring(0, 15)
            }
            val id = uniqueId.trim()
            return id
        } catch (t: Throwable) {
        }
        return "DeviceId0"
    }

    fun nativePrepareAtBeginOfFiveMinPoll() {
        if (wakeLock?.isHeld == true) {
            wakeLock?.setReferenceCounted(true)
            wakeLock?.release()
        }
        initWakelock()
        wakeLock?.acquire(60_000)  // 增加一个30s的wakelock， 扫描、连蓝牙等需要
        logMessage(TAG + "请求一个60s的WakeLock")
    }

    private fun initWakelock() {
        val manager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock =
            manager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "CareGuard:Every5MinPollLock");
    }

    private fun getSystemDefultRingtoneUri(): Uri? {
        return RingtoneManager.getActualDefaultRingtoneUri(
            this,
            RingtoneManager.TYPE_NOTIFICATION
        )
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun screenOn() {
        GlobalScope.launch {
            delay(2_1000)
            checkMqttAndRefreshFloatView()
        }
    }

    private suspend fun checkMqttAndRefreshFloatView() {
        withContext(Dispatchers.Main) {
            if (NetWorkUtils.isNetAvailable(instance())) {
                MyService.startWorkTask(instance)
            }
        }
    }

    fun startRing() {
        val model = getRingerModel()
        logMessage(TAG + "触发新消息提醒 $model")
        when (model) {
            // 铃声
            AudioManager.RINGER_MODE_NORMAL -> {
                startAlarm()
                startVibrate()
            }
            // 静音
            AudioManager.RINGER_MODE_SILENT -> {}
            // 震动
            AudioManager.RINGER_MODE_VIBRATE -> {
                startVibrate()
            }

            else -> {
                startAlarm()
                startVibrate()
            }
        }
    }

    fun startAlarm() {
        //有的手机会创建失败，从而导致mMediaPlayer为空。
        val mMediaPlayer = MediaPlayer.create(this, getSystemDefultRingtoneUri())
        mMediaPlayer.isLooping = false // 设置循环
        mMediaPlayer.setOnPreparedListener {
            it.start()
        }
        mMediaPlayer.setOnCompletionListener {
            mMediaPlayer.stop()
            mMediaPlayer.release()

        }
    }

    fun playRingByAudioRing() {
        val mRingtone = RingtoneManager.getRingtone(
            this,
            Uri.parse("android.resource://$packageName/${R.raw.warning}")
        )
        mRingtone.play()
    }

    fun playRingByAudioRingMsg() {
        val mRingtone = RingtoneManager.getRingtone(
            this,
            Uri.parse("android.resource://$packageName/${R.raw.msg}")
        )
        mRingtone.play()
    }

    fun playRing() {
        //有的手机会创建失败，从而导致mMediaPlayer为空。
        val mMediaPlayer = MediaPlayer.create(this, R.raw.warning)
        mMediaPlayer.isLooping = false // 设置循环
        mMediaPlayer.setOnPreparedListener {
            it.start()
        }
        mMediaPlayer.setOnCompletionListener {
            mMediaPlayer.stop()
            mMediaPlayer.release()
        }
    }

    fun playRingMsg() {
        //有的手机会创建失败，从而导致mMediaPlayer为空。
        val mMediaPlayer = MediaPlayer.create(this, R.raw.msg)
        mMediaPlayer.isLooping = false // 设置循环
        mMediaPlayer.setOnPreparedListener {
            it.start()
        }
        mMediaPlayer.setOnCompletionListener {
            mMediaPlayer.stop()
            mMediaPlayer.release()
        }
    }

    fun startVibrate() {
        //获取震动服务
        val vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
        //震动模式隔1秒震动1.4秒
        val pattern = longArrayOf(0, 2000)
        //震动重复，从数组的0开始（-1表示不重复）
        vibrator.vibrate(pattern, -1)
        logMessage(TAG + "触发震动")
    }

    fun getRingerModel(): Int {
        val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        return audioManager.ringerMode;
    }

    private fun getTrendSymbol(trend: String?): String {
        return when (trend) {
            "DoubleDown" -> "⇊"
            "DoubleUp" -> "⇈"
            "Flat" -> "→"
            "SingleDown" -> "↓"
            "FortyFiveDown" -> "↘︎"
            "FortyFiveUp" -> "↗︎"
            "SingleUp" -> "↑"
            else -> ""
        }
    }

    fun SHA1(context: Context): String {
        try {
            val info: PackageInfo = context.packageManager.getPackageInfo(
                context.packageName,
                PackageManager.GET_SIGNATURES
            )
            val cert = info.signatures[0].toByteArray()
            val md = MessageDigest.getInstance("SHA1")
            val publicKey = md.digest(cert)
            var hexString: StringBuffer = StringBuffer()
            for (i in publicKey) {
                val appendString = Integer.toHexString(0xFF and i.toInt()).uppercase(Locale.US)
                if (appendString.length == 1) hexString.append("0")
                hexString.append(appendString).append(":")
            }
            hexString.deleteCharAt(hexString.length - 1)
            return hexString.toString()
        } catch (e: PackageManager.NameNotFoundException) {

        } catch (e: NoSuchAlgorithmException) {

        }
        return ""
    }

//    public static String sHA1(Context context){
//        try {
//            PackageInfo info = context.getPackageManager().getPackageInfo(
//                context.getPackageName(), PackageManager.GET_SIGNATURES);
//            byte[] cert = info.signatures[0].toByteArray();
//            MessageDigest md = MessageDigest.getInstance("SHA1");
//            byte[] publicKey = md.digest(cert);
//            StringBuffer hexString = new StringBuffer();
//            for (int i = 0; i < publicKey.length; i++) {
//                String appendString = Integer.toHexString(0xFF & publicKey[i])
//                .toUpperCase(Locale.US);
//                if (appendString.length() == 1)
//                    hexString.append("0");
//                hexString.append(appendString);
//                hexString.append(":");
//            }
//            String result ＝ hexString.toString();
//            return result.substring(0, result.length()-1);
//        } catch (NameNotFoundException e) {
//            e.printStackTrace();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
}