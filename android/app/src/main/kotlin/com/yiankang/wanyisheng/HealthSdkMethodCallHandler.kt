package com.yiankang.wanyisheng

import android.content.Context
import android.util.Log
import com.tdtech.iwear.HiHealthDataQuery
import com.tdtech.iwear.TdApiConstants
import com.tdtech.iwear.TdHealthDataQuery
import com.tdtech.iwear.listener.ResultCallback
import com.tdtech.iwearkit.data.store.HiHealthDataStore
import com.tdtech.iwearkit.data.store.HiRealTimeListener
import com.tdtech.iwearkit.data.type.HiHealthPointType
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import org.json.JSONArray
import org.json.JSONObject

/**
 * 健康SDK方法调用处理器
 *
 * 负责处理来自Flutter端的各种健康设备相关操作请求，包括：
 * - 设备连接管理（连接、断开、查询、解绑）
 * - 健康数据查询（历史数据、实时数据）
 * - 设备控制（消息推送、指令发送、震动控制）
 * - 用户信息设置
 *
 * 所有方法都通过MethodChannel与Flutter端通信，返回统一的结果格式。
 *
 * 使用示例：
 * ```kotlin
 * val handler = HealthSdkMethodCallHandler(context)
 * MethodChannel(flutterEngine.dartExecutor, "com.yiankang.health_sdk/channel")
 *     .setMethodCallHandler(handler)
 * ```
 */
class HealthSdkMethodCallHandler(private val context: Context) : MethodChannel.MethodCallHandler {

    /**
     * 处理来自Flutter端的方法调用
     *
     * @param call 方法调用对象，包含方法名和参数
     * @param result 结果回调对象，用于返回处理结果
     */
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        Log.d("HealthSdkMethodCallHandler", "onMethodCall: ${call.method} ${context}")
        when (call.method) {
            /**
             * 连接指定设备
             *
             * 参数：
             * - deviceName: 设备名称
             * - deviceIdentify: 设备标识（如MAC地址）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "connectDevice" -> {
                val deviceName = call.argument<String>("deviceName")
                val deviceIdentify = call.argument<String>("deviceIdentify")
                if (deviceName == null || deviceIdentify == null) {
                    result.error("INVALID_ARGUMENT", "deviceName or deviceIdentify is null", null)
                    return
                }
                HiHealthDataStore.connectDevice(context, deviceName, deviceIdentify, object : ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to (message?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 断开当前设备连接
             *
             * 参数：
             * - deviceIdentify: 设备标识（如MAC地址）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "disconnectDevice" -> {
                val deviceIdentify = call.argument<String>("deviceIdentify")
                if (deviceIdentify == null) {
                    result.error("INVALID_ARGUMENT", "deviceIdentify is null", null)
                    return
                }
                HiHealthDataStore.disconnectDevice(context, deviceIdentify, object : ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to (message?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 查询设备信息
             *
             * 获取已配对或可用的设备列表
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - deviceInfo: 设备信息（JSON字符串）
             */
            "getDeviceList" -> {
                HiHealthDataStore.getDeviceList(context, object : ResultCallback {
                    override fun onResult(resultCode: Int, deviceInfo: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "deviceInfo" to (deviceInfo?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 解除设备配对
             *
             * 参数：
             * - deviceIdentify: 设备标识（如MAC地址）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "unbindDevice" -> {
                val deviceIdentify = call.argument<String>("deviceIdentify")
                if (deviceIdentify == null) {
                    result.error("INVALID_ARGUMENT", "deviceIdentify is null", null)
                    return
                }
                HiHealthDataStore.unbindDevice(context, deviceIdentify, object : ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to (message?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 向设备推送消息通知
             *
             * 参数：
             * - title: 通知标题
             * - text: 通知内容
             * - motor: 震动马达（1为震动，0为不震动）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "sendNotificationToDevice" -> {
                val title = call.argument<String>("title")
                val text = call.argument<String>("text")
                val motor = call.argument<Int>("motor") ?: 0
                val bundle = android.os.Bundle().apply {
                    putString("title", title)
                    putString("text", text)
                    putInt("motor", motor)
                }
                HiHealthDataStore.sendNotificationToDevice(context, bundle, object : ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to (message?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 向设备发送指令
             *
             * 参数：
             * - command: 指令内容（JSON字符串）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "sendDeviceCommand" -> {
                val command = call.argument<String>("command")
                if (command == null) {
                    result.error("INVALID_ARGUMENT", "command is null", null)
                    return
                }
                HiHealthDataStore.sendDeviceCommand(context, command, object : ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to (message?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 获取设备电量信息
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 电量信息（JSON字符串）
             */
            "getBatteryInfo" -> {
                val data = call.argument<Map<String, Any>?>("data")
                HiHealthDataStore.getBatteryInfo(context, object : ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to (message?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 发起设备震动
             *
             * 参数：
             * - isShake: true为震动，false为停止震动
             * - shakeNumber: 震动次数，最大9次
             * - shakeDuration: 震动时长（毫秒）
             * - shakeStrength: 震动强度（0: 轻震，1: 强震）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "activeShake" -> {
                val isShake = call.argument<Boolean>("isShake") ?: true
                val shakeNumber = call.argument<Int>("shakeNumber") ?: 1
                val shakeDuration = call.argument<Int>("shakeDuration") ?: 1000
                val shakeStrength = call.argument<Int>("shakeStrength") ?: 0
                HiHealthDataStore.activeShake(context, isShake, shakeNumber, shakeDuration, shakeStrength, object : ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to (message?.toString() ?: "")
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 单次测量
             *
             * 参数：
             * - devMac: 设备MAC地址
             * - api: 测量类型API字符串（如：startSingleBloodMeasure、startSingleHeartRate等）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "singleMeasure" -> {
                val devMac = call.argument<String>("devMac")
                val api = call.argument<String>("api")
                if (devMac == null || api == null) {
                    result.error("INVALID_ARGUMENT", "devMac or api is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    val jsonValObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.SET_ITEM, api)
                    jsonValObject.put(TdApiConstants.QUERY_DEV_ID, devMac)
                    jsonObject.put(TdApiConstants.SET_VALUE, jsonValObject)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(context, devMac, jsonParam, object : ResultCallback {
                        override fun onResult(resultCode: Int, message: Any?) {
                            val map = hashMapOf<String, Any?>(
                                "resultCode" to resultCode,
                                "message" to (message?.toString() ?: "")
                            )
                            result.success(map)
                        }
                    })
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 查询历史常规健康数据
             *
             * 参数：
             * - sampleType: 查询数据类型，参考HiHealthPointType常量
             * - startTime: 查询开始时间戳（单位：秒）
             * - endTime: 查询结束时间戳（单位：秒）
             * - timeout: 超时时间（毫秒，默认1000ms）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - data: 数据内容，包含funcName（unfinished/finished）和具体数据
             *
             * 注意事项：
             * - 由于设备侧数据采样算法原因，截止时间建议填写系统当前时间
             * - 数据可能分段返回，通过funcName判断是否完成
             */
            "queryHealthData" -> {
                val sampleType = call.argument<Int>("sampleType")
                val startTime = call.argument<Int>("startTime")
                val endTime = call.argument<Int>("endTime")
                val timeout = call.argument<Int>("timeout") ?: 1000

                if (sampleType == null || startTime == null || endTime == null) {
                    result.error("INVALID_ARGUMENT", "sampleType, startTime or endTime is null", null)
                    return
                }

                try {
                    val query = HiHealthDataQuery()
                    query.sampleType = sampleType
                    query.startTime = startTime.toLong()
                    query.endTime = endTime.toLong()


                    HiHealthDataStore.execQuery(context, query, timeout, object : ResultCallback {
                        override fun onResult(resultCode: Int, data: Any?) {
                            Log.e("songxi","resultCode = $resultCode")
                            Log.e("songxi","data = $data")
                            val json = JSONObject(data.toString())
                            if(resultCode == 0 && json["funcName"] == "finished"){
                                Log.e("songxi","成功并且结束")
                                val datas = JSONObject(json["value"].toString()).getJSONArray("data_frame_list")
                                Log.e("songxi","jsonArray = $datas")
                            }
                            val map = hashMapOf<String, Any?>(
                                "resultCode" to resultCode,
                                "data" to data
                            )
                            result.success(map)
                        }
                    })
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 设置用户体征信息
             *
             * 参数：
             * - userInfo: 用户信息Map，包含以下可选字段：
             *   - height: 身高（厘米，范围：0-255）
             *   - weight: 体重（千克，范围：0-255）
             *   - gender: 性别（1: 男性，2: 女性）
             *   - birthday: 生日（格式：YYYYMMDD，不能设置未来日期）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 结果信息或错误描述
             */
            "setUserPhysicalInfo" -> {
                val userInfoMap = call.argument<Map<String, Any>>("userInfo")
                if (userInfoMap == null) {
                    result.error("INVALID_ARGUMENT", "userInfo is null", null)
                    return
                }
                try {
                    val userInfo = com.tdtech.iwearkit.data.HiHealthUserInfoDatas()
                    userInfoMap["height"]?.let { userInfo.setHeight((it as Number).toInt()) }
                    userInfoMap["weight"]?.let { userInfo.setWeight((it as Number).toFloat()) }
                    userInfoMap["gender"]?.let { userInfo.setGender((it as Number).toInt()) }
                    userInfoMap["birthday"]?.let { userInfo.setBirthday((it as Number).toInt()) }
                    HiHealthDataStore.setDeviceUserInfo(context, userInfo, object : com.tdtech.iwear.listener.ResultCallback {
                        override fun onResult(resultCode: Int, message: Any?) {
                            val map = hashMapOf<String, Any?>(
                                "resultCode" to resultCode,
                                "message" to message
                            )
                            result.success(map)
                        }
                    })
                } catch (e: Exception) {
                    result.error("SET_USER_INFO_ERROR", e.message, null)
                }
            }

            /**
             * 设置心率测量模式
             *
             * 参数：
             * - status: 开关状态（0: 关闭，1: 自动（智能）测量，2: 连续（实时）测量，部分设备不支持2）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 处理结果信息（通常为null）
             */
            "setHeartRateMeasureMode" -> {
                val status = call.argument<Int>("status")
                if (status == null) {
                    result.error("INVALID_ARGUMENT", "status is null", null)
                    return
                }
                HiHealthDataStore.setContinueMeasureHeartRateEnable(context, status, object : com.tdtech.iwear.listener.ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to message
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 设置静态心率提醒门限
             *
             * 参数：
             * - type: 提醒类型（"raise": 过高提醒，"down": 过低提醒）
             * - enable: 提醒开关（true: 打开，false: 关闭）
             * - number: 心率提醒门限
             *   - 过高提醒取值范围：60 <= number <= 220
             *   - 过低提醒取值范围：10 <= number <= 100
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 处理结果信息（通常为null）
             */
            "setHeartRateRemind" -> {
                val type = call.argument<String>("type")
                val enable = call.argument<Boolean>("enable")
                val number = call.argument<Int>("number")

                if (type == null || enable == null || number == null) {
                    result.error("INVALID_ARGUMENT", "type, enable or number is null", null)
                    return
                }

                // 验证参数范围
                if (type == "raise" && (number < 60 || number > 220)) {
                    result.error("INVALID_ARGUMENT", "过高提醒门限应在60-220之间", null)
                    return
                }
                if (type == "down" && (number < 10 || number > 100)) {
                    result.error("INVALID_ARGUMENT", "过低提醒门限应在10-100之间", null)
                    return
                }

                HiHealthDataStore.setHeartRateRemind(context, type, enable, number, object : com.tdtech.iwear.listener.ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to message
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 设置科学睡眠开关
             *
             * 参数：
             * - status: 开关状态（0: 关闭，1: 打开）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 处理结果信息（通常为null）
             */
            "setCoreSleepEnable" -> {
                val status = call.argument<Int>("status")
                if (status == null) {
                    result.error("INVALID_ARGUMENT", "status is null", null)
                    return
                }

                // 验证参数值
                if (status != 0 && status != 1) {
                    result.error("INVALID_ARGUMENT", "status must be 0 or 1", null)
                    return
                }

                HiHealthDataStore.setCoreSleepEnable(context, status, object : com.tdtech.iwear.listener.ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to message
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 设备开关设置通用接口
             *
             * 参数：
             * - name: 开关名称（如：'bt_lost_remind_enable', 'auto_light_screen_enable', 'activity_reminder_enable', 'auto_spo2_measure_enable', 'is_enable_hwhealth'）
             * - enable: 开关状态（true: 打开，false: 关闭）
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 处理结果信息（通常为null）
             *
             * 支持的开关名称：
             * - 'bt_lost_remind_enable'：蓝牙断开提醒开关
             * - 'auto_light_screen_enable'：抬腕亮屏开关
             * - 'activity_reminder_enable'：久坐提醒开关
             * - 'auto_spo2_measure_enable'：血氧自动测量开关
             * - 'is_enable_hwhealth'：允许运动健康连接设备
             */
            "setDeviceSwitchEnable" -> {
                val name = call.argument<String>("name")
                val enable = call.argument<Boolean>("enable")
                if (name == null || enable == null) {
                    result.error("INVALID_ARGUMENT", "name or enable is null", null)
                    return
                }
                HiHealthDataStore.setDeviceSwitchEnable(context, name, enable, object : com.tdtech.iwear.listener.ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to message
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 设置血氧过低提醒开关
             *
             * 参数：
             * - remindEnable: 开关状态（1: 打开，0: 关闭）
             * - downLimit: 血氧提醒门限值，有效范围[60,100]
             *
             * 返回：
             * - resultCode: 结果码（0: 成功，其他: 失败）
             * - message: 处理结果信息（通常为null或错误信息）
             */
            "setBloodOxygenDownRemind" -> {
                val remindEnable = call.argument<Int>("remindEnable")
                val downLimit = call.argument<Int>("downLimit")
                if (remindEnable == null || downLimit == null) {
                    result.error("INVALID_ARGUMENT", "remindEnable or downLimit is null", null)
                    return
                }
                if (downLimit < 60 || downLimit > 100) {
                    result.error("INVALID_ARGUMENT", "downLimit must be in [60,100]", null)
                    return
                }
                HiHealthDataStore.setBloodOxygenDownRemind(context, remindEnable, downLimit, object : com.tdtech.iwear.listener.ResultCallback {
                    override fun onResult(resultCode: Int, message: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "resultCode" to resultCode,
                            "message" to message
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 设置通知开关
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - enable: 开关状态（true: 打开，false: 关闭）
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             */
            "setNotificationSwitch" -> {
                val devMac = call.argument<String>("devMac")
                val enable = call.argument<Boolean>("enable")
                if (devMac == null || enable == null) {
                    result.error("INVALID_ARGUMENT", "devMac or enable is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.SET_ITEM, TdApiConstants.NOTIFICATION_SWITCH)
                    jsonObject.put(TdApiConstants.SET_VALUE, if (enable) 1 else 0)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(
                        context,
                        devMac,
                        jsonParam,
                        object : com.tdtech.iwear.listener.ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 设置SDK日志打印级别
             *
             * 参数：
             * - isEnable: 是否启用日志
             * - logLevel: 日志级别，取值范围[2,7]，对应android.util.Log.VERBOSE 到 ASSERT
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             *
             * 日志保存路径：sdcard/Android/data/应用包名/files/wearable/
             */
            "setSDKLogEnableAndLevel" -> {
                val isEnable = call.argument<Boolean>("isEnable")
                val logLevel = call.argument<Int>("logLevel")
                if (isEnable == null || logLevel == null) {
                    result.error("INVALID_ARGUMENT", "isEnable or logLevel is null", null)
                    return
                }
                if (logLevel < 2 || logLevel > 7) {
                    result.error("INVALID_ARGUMENT", "logLevel must be in [2,7]", null)
                    return
                }
                HiHealthDataStore.setSDKLogEnableAndLevel(context, isEnable, logLevel, object : com.tdtech.iwear.listener.ResultCallback {
                    override fun onResult(code: Int, data: Any?) {
                        val map = hashMapOf<String, Any?>(
                            "code" to code,
                            "data" to data
                        )
                        result.success(map)
                    }
                })
            }

            /**
             * 设置ECG启用禁用开关
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - enable: 开关状态（true: 打开，false: 关闭）
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             */
            "setECGSwitchEnable" -> {
                val devMac = call.argument<String>("devMac")
                val enable = call.argument<Boolean>("enable")
                if (devMac == null || enable == null) {
                    result.error("INVALID_ARGUMENT", "devMac or enable is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    val jsonValObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.SET_ITEM, TdApiConstants.ACTIVE_ECG_SWITCH)
                    jsonValObject.put("setEnable", if (enable) 1 else 0)
                    jsonObject.put(TdApiConstants.SET_VALUE, jsonValObject)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(
                        context,
                        devMac,
                        jsonParam,
                        object : com.tdtech.iwear.listener.ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 设置压力自动测量开关
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - enable: 开关状态（true: 打开，false: 关闭）
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             */
            "setStressAutoMeasurementSwitch" -> {
                val devMac = call.argument<String>("devMac")
                val enable = call.argument<Boolean>("enable")
                if (devMac == null || enable == null) {
                    result.error("INVALID_ARGUMENT", "devMac or enable is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    val jsonValObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.SET_ITEM, TdApiConstants.STRESS_AUTO_MEASURE_SWITCH)
                    jsonValObject.put("setEnable", if (enable) 1 else 0)
                    jsonObject.put(TdApiConstants.SET_VALUE, jsonValObject)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(
                        context,
                        devMac,
                        jsonParam,
                        object : com.tdtech.iwear.listener.ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 设置动态血压周期及频率
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - planStartTime: 计划开始时间（时间戳，单位秒）
             * - nightStartTime: 夜间开始时间（时间戳，单位秒）
             * - nightEndTime: 夜间结束时间（时间戳，单位秒）
             * - nightFreq: 夜间频率
             * - dayFreq: 白天频率
             * - measureMode: 测量模式（0: 手动，1: 自动）
             * - planStatus: 计划状态（1: 开始计划，2: 终止计划）
             * - planRepeat: 星期测量计划（如 "1,2,3,4,5"）
             * - setEnable: 夜间测量开关（1: 开，0: 关）
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             */
            "setDynamicBloodPressurePlan" -> {
                val devMac = call.argument<String>("devMac")
                val planStartTime = call.argument<Int>("planStartTime")
                val nightStartTime = call.argument<Int>("nightStartTime")
                val nightEndTime = call.argument<Int>("nightEndTime")
                val nightFreq = call.argument<Int>("nightFreq")
                val dayFreq = call.argument<Int>("dayFreq")
                val measureMode = call.argument<Int>("measureMode")
                val planStatus = call.argument<Int>("planStatus")
                val planRepeat = call.argument<String>("planRepeat")
                val setEnable = call.argument<Int>("setEnable")
                if (devMac == null || planStartTime == null || nightStartTime == null || nightEndTime == null ||
                    nightFreq == null || dayFreq == null || measureMode == null || planStatus == null ||
                    planRepeat == null || setEnable == null) {
                    result.error("INVALID_ARGUMENT", "参数不能为空", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    val jsonValObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.SET_ITEM, TdApiConstants.SET_DYNAMIC_BP_DETECTED)
                    jsonValObject.put(TdApiConstants.PLAN_START_TIME, planStartTime)
                    jsonValObject.put(TdApiConstants.NIGHT_START_TIME, nightStartTime)
                    jsonValObject.put(TdApiConstants.NIGHT_END_TIME, nightEndTime)
                    jsonValObject.put(TdApiConstants.NIGHT_FREQ, nightFreq)
                    jsonValObject.put(TdApiConstants.DAY_FREQ, dayFreq)
                    jsonValObject.put(TdApiConstants.MEASURE_MODE, measureMode)
                    jsonValObject.put(TdApiConstants.PLAN_STATUS, planStatus)
                    jsonValObject.put(TdApiConstants.PLAN_REPEAT, planRepeat)
                    jsonValObject.put(TdApiConstants.SET_ENABLE, setEnable)
                    jsonObject.put(TdApiConstants.SET_VALUE, jsonValObject)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(
                        context,
                        devMac,
                        jsonParam,
                        object : com.tdtech.iwear.listener.ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 设置睡眠呼吸暂停开关
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - enable: 开关状态（true: 打开，false: 关闭）
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             */
            "setSleepBreathePauseSwitch" -> {
                val devMac = call.argument<String>("devMac")
                val enable = call.argument<Boolean>("enable")
                if (devMac == null || enable == null) {
                    result.error("INVALID_ARGUMENT", "devMac or enable is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    val jsonValObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.SET_ITEM, TdApiConstants.SLEEP_BREATHE_PAUSE_SWITCH)
                    jsonValObject.put(TdApiConstants.SET_ENABLE, if (enable) 1 else 0)
                    jsonObject.put(TdApiConstants.SET_VALUE, jsonValObject)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(
                        context,
                        devMac,
                        jsonParam,
                        object : com.tdtech.iwear.listener.ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 设置脉搏波开关/自动测量开关
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - enable: 开关状态（true: 打开，false: 关闭）
             * - type: 类型（0: 脉搏波功能开关，1: 自动测量开关）
             *   - 如果要设置自动测量开关(type=1)，需要先打开功能开关(type=0, enable=true)
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             */
            "setPulseWaveSwitch" -> {
                val devMac = call.argument<String>("devMac")
                val enable = call.argument<Boolean>("enable")
                val type = call.argument<Int>("type")
                if (devMac == null || enable == null || type == null) {
                    result.error("INVALID_ARGUMENT", "devMac, enable or type is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    val jsonValObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.SET_ITEM, TdApiConstants.PULSE_WAVE_SWITCH)
                    jsonValObject.put(TdApiConstants.SET_ENABLE, if (enable) 1 else 0)
                    jsonValObject.put("type", type)
                    jsonObject.put(TdApiConstants.SET_VALUE, jsonValObject)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(
                        context,
                        devMac,
                        jsonParam,
                        object : com.tdtech.iwear.listener.ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 查询设备是否支持联系人设置
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息或结果对象（如 {"setEnable": true/false}）
             */
            "querySupportSetContacts" -> {
                val devMac = call.argument<String>("devMac")
                if (devMac == null) {
                    result.error("INVALID_ARGUMENT", "devMac is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.QUERY_ITEM, "frequentContactsEnable")
                    jsonObject.put(TdApiConstants.QUERY_VALUE, "")
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.query(
                        context,
                        devMac,
                        jsonParam,
                        object : com.tdtech.iwear.listener.ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 获取常用联系人支持数量
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息或结果对象（如 {"max": 10}）
             */
            "querySupportMaxContactsCount" -> {
                val devMac = call.argument<String>("devMac")
                if (devMac == null) {
                    result.error("INVALID_ARGUMENT", "devMac is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.QUERY_ITEM, "frequentContactsNumber")
                    jsonObject.put(TdApiConstants.QUERY_VALUE, "")
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.query(
                        context,
                        devMac,
                        jsonParam,
                        object : ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 设置常用联系人数据
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - contactsList: 联系人列表（List<Map<String, Object>>，会自动转为JSON字符串）
             *
             * 返回：
             * - code: 结果码（0: 成功，其他: 失败）
             * - data: 接口调用错误信息（通常为null或错误信息）
             */
            "setFrequentContactsData" -> {
                val devMac = call.argument<String>("devMac")
                val contactsList = call.argument<List<Map<String, Any>>>("contactsList")
                if (devMac == null || contactsList == null) {
                    result.error("INVALID_ARGUMENT", "devMac or contactsList is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    val valueObj = org.json.JSONObject()
                    val gson = com.google.gson.GsonBuilder().create()
                    valueObj.put("contactsList", gson.toJson(contactsList))
                    jsonObject.put(TdApiConstants.SET_ITEM, "frequentContactsData")
                    jsonObject.put(TdApiConstants.SET_VALUE, valueObj)
                    val jsonParam = jsonObject.toString()
                    HiHealthDataStore.set(
                        context,
                        devMac,
                        jsonParam,
                        object : ResultCallback {
                            override fun onResult(code: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>(
                                    "code" to code,
                                    "data" to data
                                )
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("JSON_ERROR", e.message, null)
                }
            }

            /**
             * 查询科学睡眠数据
             *
             * 参数：
             * - startTime: 查询睡眠数据范围的起始时间戳（单位：秒）
             * - endTime: 查询睡眠数据范围的结束时间戳（单位：秒）
             * - age: 用户年龄
             * - gender: 用户性别（0：女性，1：男性）
             *
             * 返回：
             * - resultCode: 处理结果码（0：成功，-2：超时，-3：丢失，其他：失败）
             * - message: 错误信息或JSON数据（见文档数据结构说明）
             */
            "queryCoreSleepData" -> {
                val startTime = call.argument<Long>("startTime")
                val endTime = call.argument<Long>("endTime")
                val age = call.argument<Int>("age")
                val gender = call.argument<Int>("gender")
                if (startTime == null || endTime == null || age == null || gender == null) {
                    result.error("INVALID_ARGUMENT", "startTime, endTime, age or gender is null", null)
                    return
                }
                try {
                   HiHealthDataStore.execQueryCoreSleepResult(
                        context,
                        startTime,
                        endTime,
                        age,
                        gender,
                        object : ResultCallback {
                            override fun onResult(resultCode: Int, message: Any?) {
                                val map = hashMapOf<String, Any?>("resultCode" to resultCode, "message" to message)
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 查询压力数据
             *
             * 参数：
             * - startTime: 查询数据范围的开始时间戳（单位：秒）
             * - endTime: 查询数据范围的结束时间戳（单位：秒）
             *
             * 返回：
             * - resultCode: 处理结果码（0：成功，-2：超时，-3：丢失，其他：失败）
             * - data: 错误信息或JSON数据（score, start_time, end_time）
             */
            "queryStressData" -> {
                val startTime = call.argument<Int>("startTime")
                val endTime = call.argument<Int>("endTime")
                if (startTime == null || endTime == null) {
                    result.error("INVALID_ARGUMENT", "startTime or endTime is null", null)
                    return
                }
                try {
                    val query = HiHealthDataQuery()
                    query.sampleType = HiHealthPointType.DATA_POINT_STRESS
                    query.startTime = startTime.toLong()
                    query.endTime = endTime.toLong()
                    HiHealthDataStore.execQuery(
                        context,
                        query,
                        0,
                        object : ResultCallback {
                            override fun onResult(resultCode: Int, data: Any?) {
                                val map = hashMapOf<String, Any?>("resultCode" to resultCode, "data" to data)
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 查询历史体温数据
             *
             * 参数：
             * - startTime: 查询数据范围的开始时间戳（单位：毫秒）
             * - endTime: 查询数据范围的结束时间戳（单位：毫秒）
             * - msgType: 查询模块类型（如：1 表示体温&皮肤温度）
             * - digiTypeId: 业务类型ID（体温:400011, 皮肤温度:400012）
             *
             * 返回：
             * - resultCode: 处理结果码（0：成功，-2：超时，-3：丢失，其他：失败）
             * - message: 错误信息或JSON数据（见文档数据结构说明）
             */
            "queryTemperatureHistoryData" -> {
                val startTime = call.argument<Long>("startTime")
                val endTime = call.argument<Long>("endTime")
                val msgType = call.argument<Int>("msgType")
                val digiTypeId = call.argument<Int>("digiTypeId")
                if (startTime == null || endTime == null || msgType == null || digiTypeId == null) {
                    result.error("INVALID_ARGUMENT", "startTime, endTime, msgType or digiTypeId is null", null)
                    return
                }
                try {
                    val query = TdHealthDataQuery()
                    query.startTime = startTime
                    query.endTime = endTime
                    query.msgType = msgType
                    query.digiTypeId = digiTypeId
                    HiHealthDataStore.execTdQuery(
                        context,
                        query,
                        object : ResultCallback {
                            override fun onResult(resultCode: Int, message: Any?) {
                                val map = hashMapOf<String, Any?>("resultCode" to resultCode, "message" to message)
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 查询历史血压数据
             *
             * 参数：
             * - startTime: 开始时间时间戳（毫秒）
             * - endTime: 结束时间时间戳（毫秒）
             *
             * 返回：
             * - resultCode: 处理结果码（0或100000：成功，其他：失败）
             * - message: 数据结构或错误提示信息（见文档说明）
             */
            "queryBloodPressureHistoryData" -> {
                val startTime = call.argument<Long>("startTime")
                val endTime = call.argument<Long>("endTime")
                if (startTime == null || endTime == null) {
                    result.error("INVALID_ARGUMENT", "startTime or endTime is null", null)
                    return
                }
                try {
                    HiHealthDataStore.getBloodPressureData(
                        context,
                        startTime,
                        endTime,
                        object : ResultCallback {
                            override fun onResult(resultCode: Int, message: Any?) {
                                val map = hashMapOf<String, Any?>("resultCode" to resultCode, "message" to message)
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 查询历史心电图数据
             *
             * 参数：
             * - startTime: 开始时间时间戳（秒）
             * - endTime: 结束时间时间戳（秒）
             * - needRawDataPoints: 是否包含原始数据点
             *
             * 返回：
             * - resultCode: 处理结果码（0或100000：成功，其他：失败）
             * - value: 数据结构或错误提示信息（见文档说明）
             */
            "queryEcgHistoryData" -> {
                val startTime = call.argument<Long>("startTime")
                val endTime = call.argument<Long>("endTime")
                val needRawDataPoints = call.argument<Boolean>("needRawDataPoints")
                if (startTime == null || endTime == null || needRawDataPoints == null) {
                    result.error("INVALID_ARGUMENT", "startTime, endTime or needRawDataPoints is null", null)
                    return
                }
                try {
                    HiHealthDataStore.queryHistoryEcgData(
                        context,
                        startTime,
                        endTime,
                        needRawDataPoints,
                        object : ResultCallback {
                            override fun onResult(resultCode: Int, value: Any?) {
                                val map = hashMapOf<String, Any?>("resultCode" to resultCode, "value" to value)
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 查询历史睡眠呼吸暂停数据
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - jsonParam: Json字符串，结构体包含item、value（startTime, endTime）
             *
             * 返回：
             * - code: 状态码（0：成功，100000：数据返回，200000：数据返回结束，其他：失败）
             * - data: code为100000时为数据信息（json结构），其他为错误信息
             */
            "querySleepBreathePauseHistoryData" -> {
                val devMac = call.argument<String>("devMac")
                val jsonParamMap = call.argument<Map<String, Any>>("jsonParam")
                if (devMac == null || jsonParamMap == null) {
                    result.error("INVALID_ARGUMENT", "devMac or jsonParam is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.QUERY_ITEM, jsonParamMap["item"] ?: "sleepBreathePauseData")
                    val valueMap = jsonParamMap["value"] as? Map<*, *>
                    val valueObject = org.json.JSONObject()
                    valueMap?.get("startTime")?.let { valueObject.put(TdApiConstants.QUERY_START_TIME, it) }
                    valueMap?.get("endTime")?.let { valueObject.put(TdApiConstants.QUERY_END_TIME, it) }
                    jsonObject.put(TdApiConstants.QUERY_VALUE, valueObject)
                    val jsonParamStr = jsonObject.toString()
                    HiHealthDataStore.query(context,devMac,jsonParamStr,object : ResultCallback {
                        override fun onResult(code: Int, data: Any?) {
                            val map = hashMapOf<String, Any?>(
                                "code" to code,
                                "data" to data
                            )
                            result.success(map)
                        }
                    })
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 查询历史脉搏波心率失常分析数据
             *
             * 参数：
             * - devMac: 已连接的穿戴设备MAC地址
             * - jsonParam: Json字符串，结构体包含item、value（startTime, endTime）
             *
             * 返回：
             * - code: 状态码（0：成功，100000：数据返回，200000：数据返回结束，其他：失败）
             * - data: code为100000时为数据信息（json结构），其他为错误信息
             */
            "queryPulseWaveHistoryData" -> {
                val devMac = call.argument<String>("devMac")
                val jsonParamMap = call.argument<Map<String, Any>>("jsonParam")
                if (devMac == null || jsonParamMap == null) {
                    result.error("INVALID_ARGUMENT", "devMac or jsonParam is null", null)
                    return
                }
                try {
                    val jsonObject = org.json.JSONObject()
                    jsonObject.put(TdApiConstants.QUERY_ITEM, jsonParamMap["item"] ?: "pulseWaveData")
                    val valueMap = jsonParamMap["value"] as? Map<*, *>
                    val valueObject = org.json.JSONObject()
                    valueMap?.get("startTime")?.let { valueObject.put(TdApiConstants.QUERY_START_TIME, it) }
                    valueMap?.get("endTime")?.let { valueObject.put(TdApiConstants.QUERY_END_TIME, it) }
                    jsonObject.put(TdApiConstants.QUERY_VALUE, valueObject)
                    val jsonParamStr = jsonObject.toString()
                    HiHealthDataStore.query(context,devMac,jsonParamStr,object : ResultCallback {
                        override fun onResult(code: Int, data: Any?) {
                            val map = hashMapOf<String, Any?>(
                                "code" to code,
                                "data" to data
                            )
                            result.success(map)
                        }
                    })
                } catch (e: Exception) {
                    result.error("QUERY_ERROR", e.message, null)
                }
            }

            /**
             * 关闭设备心率告警上报功能
             *
             * 返回：
             * - resultCode: 处理结果码（0：成功，其他：失败）
             * - message: 提示信息，可能为null
             */
            "closeHeartRateWarnReport" -> {
                try {
                    HiHealthDataStore.closeHearRateWarnReport(
                        context,
                        object : ResultCallback {
                            override fun onResult(resultCode: Int, message: Any?) {
                                val map = hashMapOf<String, Any?>("resultCode" to resultCode, "message" to message)
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("CLOSE_ERROR", e.message, null)
                }
            }

            /**
             * 关闭血氧告警上报
             *
             * 返回：
             * - resultCode: 处理结果码（0：成功，其他：失败）
             * - message: 提示信息，可能为null
             */
            "closeBloodOxygenDownWarnReport" -> {
                try {
                    HiHealthDataStore.closeBloodOxygenDownReport(
                        context,
                        object : ResultCallback {
                            override fun onResult(resultCode: Int, message: Any?) {
                                val map = hashMapOf<String, Any?>("resultCode" to resultCode, "message" to message)
                                result.success(map)
                            }
                        }
                    )
                } catch (e: Exception) {
                    result.error("CLOSE_ERROR", e.message, null)
                }
            }

            // 未知方法
            else -> result.notImplemented()
        }
    }
}