package com.yiankang.wanyisheng

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast

class WatchButtonEventReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        if (intent != null && "com.tdtech.wear.action.EVENT_REPORT" == intent.action) {
            val eventInfo = intent.getStringExtra("data") ?: ""
            Log.i("WatchButtonEventReceiver", "eventInfo: $eventInfo")
            Toast.makeText(context, "按键上报！", Toast.LENGTH_LONG).show()
            // 通知Flutter
            val app = context.applicationContext as? CustomApplication
            val channel = app?.healthSdkChannel
            channel?.invokeMethod(
                "onEventReport",
                mapOf("eventInfo" to eventInfo)
            )
        }
    }
} 