package com.yiankang.wanyisheng

import android.content.pm.PackageManager
import android.util.Log
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity: FlutterActivity(){

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
    }


    override fun getCachedEngineId(): String {
        return CustomApplication.FLUTTER_ENGINE_CACHE_ID
    }

    override fun onResume() {
        super.onResume()
        MyService.startService(this)
    }

}
