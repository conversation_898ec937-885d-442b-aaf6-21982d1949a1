group 'com.fluttercandies.flutter_image_compress'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.8.20'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.fluttercandies.flutter_image_compress'
    }

    compileSdkVersion 34

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    compileOptions {
        // Sets Java compatibility to Java 11
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.exifinterface:exifinterface:1.3.3'
    implementation 'androidx.heifwriter:heifwriter:1.0.0'
//    implementation 'commons-io:commons-io:2.6'
}
