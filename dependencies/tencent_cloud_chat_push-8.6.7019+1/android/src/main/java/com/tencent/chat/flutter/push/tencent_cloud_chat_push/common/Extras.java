package com.tencent.chat.flutter.push.tencent_cloud_chat_push.common;

public interface Extras {
    String BUSINESS_ID = "business_id";
    String PUSH_TOKEN = "push_token";
    String ANDROID_CONFIGS = "configs";
    String PUSH_CONFIG_JSON = "push_config_json";
    String FCM_PUSH_CHANNEL_ID = "fcm_push_channel_id";
    String PRIVATE_RING_NAME = "private_ring_name";
    String BRAND_ID = "brand_id";
    String REGION = "region";
    String ENABLE_FCM_PRIVATE_RING = "enable_fcm_private_ring";
    String ON_NOTIFICATION_CLICKED = "on_notification_clicked";
    String ON_APP_WAKE_UP = "on_app_wake_up";

    String FLUTTER_ENGINE = "tencent_cloud_chat_push_flutter_engine";
    String SHOW_IN_FOREGROUND = "show_in_foreground";
    String SDK_APP_ID = "sdkAppId";
    String APP_KEY = "appKey";
    String REGISTRATION_ID = "registrationID";
    String DISABLE_POST_NOTIFICATION_IN_FOREGROUND = "disablePostNotificationInForeground";
    String FORCE_USE_FCM_PUSH_CHANNEL = "forceUseFCMPushChannel";
    String CHANNEL_ID = "channel_id";
    String CHANNEL_NAME = "channel_name";
    String CHANNEL_DESC = "channel_desc";
    String CHANNEL_SOUND = "channel_sound";
    String CUSTOM_CONFIGFILE = "configs";
}
