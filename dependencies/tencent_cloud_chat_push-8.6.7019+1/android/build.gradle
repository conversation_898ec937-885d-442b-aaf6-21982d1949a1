group 'com.tencent.chat.flutter.push.tencent_cloud_chat_push'
version '1.0'

buildscript {
    repositories {
        google()
        jcenter()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.tencent.chat.flutter.push.tencent_cloud_chat_push'
    }

    compileSdkVersion 33

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 21
    }

    dependencies {
        testImplementation 'junit:junit:4.13.2'
        testImplementation 'org.mockito:mockito-core:5.0.0'
    }

    testOptions {
        unitTests.all {
            testLogging {
                events "passed", "skipped", "failed", "standardOut", "standardError"
                outputs.upToDateWhen { false }
                showStandardStreams = true
            }
        }
    }
}

dependencies {
    implementation 'com.tencent.timpush:timpush:8.6.7019'
    api "com.tencent.liteav.tuikit:tuicore:8.6.7019"
}

