<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/tuicallkit_bg_incoming_view"
    android:orientation="horizontal">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/img_float_avatar"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="16dp"
        android:scaleType="centerCrop"
        android:src="@drawable/tuicallkit_ic_avatar"
        app:round="8dp" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_float_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFFFF"
            android:textSize="18sp"
            tools:hint="inviter" />

        <TextView
            android:id="@+id/tv_float_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="#FFFFFFFF"
            android:textSize="12sp"
            tools:hint="description" />
    </LinearLayout>

    <ImageView
        android:id="@+id/btn_float_decline"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:src="@drawable/tuicallkit_bg_hangup" />

    <ImageView
        android:id="@+id/btn_float_accept"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp"
        android:src="@drawable/tuicallkit_bg_dialing" />
</LinearLayout>
