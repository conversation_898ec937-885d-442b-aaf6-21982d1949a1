<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/ll_notification"
    android:background="#00000000"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/img_incoming_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="16dp"
        android:scaleType="centerCrop"
        android:src="@drawable/tuicallkit_ic_avatar" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_incoming_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Compat.Notification.Title"
            android:textSize="18sp"
            tools:hint="inviter" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_media_type"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="4dp"
                android:src="@drawable/tuicallkit_ic_float" />

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textAppearance="@style/TextAppearance.Compat.Notification.Line2"
                android:textSize="12sp"
                tools:hint="description" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/btn_decline"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="24dp"
        android:src="@drawable/tuicallkit_bg_hangup" />

    <ImageView
        android:id="@+id/btn_accept"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp"
        android:src="@drawable/tuicallkit_bg_dialing" />
</LinearLayout>
