<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.tencent.cloud.tuikit.flutter.tuicallkit">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <application>

        <provider
            android:name="com.tencent.cloud.tuikit.flutter.tuicallkit.service.ServiceInitializer"
            android:authorities="${applicationId}.ServiceInitializer"
            android:enabled="true"
            android:exported="false" />

        <service
            android:name="com.tencent.cloud.tuikit.flutter.tuicallkit.service.ForegroundService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <service
            android:name="com.tencent.cloud.tuikit.flutter.tuicallkit.view.floatwindow.FloatWindowService"
            android:enabled="true"
            android:exported="false" />

        <receiver
            android:name="com.tencent.cloud.tuikit.flutter.tuicallkit.view.incomingfloatwindow.IncomingCallReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="handle_call_received"/>
                <action android:name="accept_call_action" />
                <action android:name="reject_call_action" />
            </intent-filter>
        </receiver>
    </application>
</manifest>
