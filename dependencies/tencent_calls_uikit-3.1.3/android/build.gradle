group 'com.tencent.cloud.tuikit.flutter.tuicallkit'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 34
    if (project.android.hasProperty("namespace")) {
        namespace "com.tencent.cloud.tuikit.flutter.tuicallkit"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 19
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.3.1'
    api 'com.google.code.gson:gson:2.9.1'
    api 'com.tencent.liteav.tuikit:tuicore:8.5.6864'
    api 'com.google.android.material:material:1.4.0'
    api 'androidx.constraintlayout:constraintlayout:1.1.3'
    api 'com.github.bumptech.glide:glide:4.12.0'

     def projects = this.rootProject.getAllprojects().stream().map { project -> project.name }.collect()
    println "all projects : {$projects}"
    if (projects.contains("engine_source")) {
        api project(':engine_source')
    } else {
        api rootProject.getProperties().containsKey("liteavSdk") ? rootProject.ext.liteavSdk : "com.tencent.liteav:LiteAVSDK_Professional:12.4.0+"
        api rootProject.getProperties().containsKey("roomEngineSdk") ? rootProject.ext.roomEngineSdk : "io.trtc.uikit:rtc_room_engine:3.1+"
    }
}